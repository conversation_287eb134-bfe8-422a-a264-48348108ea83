# **App Name**: Prompt Showdown

## Core Features:

- Multiplayer Arena: Real-time multiplayer arena with Socket.IO for synchronized challenges and scoring.
- Challenge Display: Display of coding challenges with clear instructions and input fields.
- Prompt Evaluation: AI-powered prompt evaluation using <PERSON> for scoring prompt effectiveness. This tool analyzes submissions based on specificity and completeness.
- Real-time Leaderboard: Real-time leaderboard to track player scores and rankings during challenges.
- Arcade UI: Gamified user interface with retro aesthetics, animations, and dynamic timers.
- Prompt selection: System to select a prompt from multiple choices based on the quality and coverage of its output

## Style Guidelines:

- Primary color: Vibrant magenta (#FF00FF) to capture the retro-futuristic Retrowave theme, drawing inspiration from Hotline Miami's color palette.
- Background color: Dark grey (#222222) to provide high contrast for the neon color scheme and enhance readability in a dark environment.
- Accent color: Electric blue (#7DF9FF), which is analogous to magenta and enhances the futuristic arcade vibe, adding visual punch to interactive elements.
- Body and headline font: 'Space Grotesk' (sans-serif) for a computerized and techy feel.
- Use neon-glowing icons inspired by 80s arcade games.
- Retro-style animations and transitions between challenges.
- Full-screen responsive design, optimizing for different devices.