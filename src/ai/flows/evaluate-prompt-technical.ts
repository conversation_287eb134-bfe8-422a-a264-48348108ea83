
'use server';

/**
 * @fileOverview This file defines a Genkit flow for evaluating a C# prompt
 * designed to retrieve the Dólar Blue quote from a public API.
 *
 * The flow assesses the prompt's clarity, technical specificity, completeness,
 * and robustness to provide a score and detailed feedback in Spanish.
 *
 * - evaluatePromptTechnical - The function to evaluate the C# prompt.
 * - EvaluatePromptTechnicalInput - The input type for the evaluatePromptTechnical function.
 * - EvaluatePromptTechnicalOutput - The return type for the evaluatePromptTechnical function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const EvaluatePromptTechnicalInputSchema = z.object({
  prompt: z.string().describe('The C# prompt to evaluate.'),
});
export type EvaluatePromptTechnicalInput = z.infer<
  typeof EvaluatePromptTechnicalInputSchema
>;

const EvaluatePromptTechnicalOutputSchema = z.object({
  score: z
    .number()
    .describe(
      'La puntuación (0-100) que representa la calidad del prompt basada en especificidad, completitud, claridad y robustez.'
    ),
  evaluationDetails: z
    .string()
    .describe(
      'Feedback detallado en ESPAÑOL sobre el prompt, incluyendo fortalezas y áreas de mejora.'
    ),
});
export type EvaluatePromptTechnicalOutput = z.infer<
  typeof EvaluatePromptTechnicalOutputSchema
>;

export async function evaluatePromptTechnical(
  input: EvaluatePromptTechnicalInput
): Promise<EvaluatePromptTechnicalOutput> {
  return evaluatePromptTechnicalFlow(input);
}

const prompt = ai.definePrompt({
  name: 'evaluatePromptTechnicalPrompt',
  input: {schema: EvaluatePromptTechnicalInputSchema},
  output: {schema: EvaluatePromptTechnicalOutputSchema},
  prompt: `Eres un experto en ingeniería de prompts de IA especializado en la generación de código C#.
Tu tarea es evaluar rigurosamente la calidad de un prompt en C# diseñado para generar una aplicación de consola
que recupere la cotización del Dólar Blue desde una API pública como bluelytics.com.ar.

La aplicación DEBE devolver el valor de compra, el valor de venta y la fecha de la última actualización en un ÚNICO formato JSON.
El prompt debe ser lo suficientemente preciso para que una IA infiera una estructura JSON lógica SIN que el usuario defina explícitamente esa estructura.

Evalúa el prompt basándote en los siguientes criterios y asigna una puntuación entre 0 y 100. Proporciona tu feedback detallado en ESPAÑOL.

*   A. Especificidad Técnica (30 puntos):
    *   Lenguaje (5 pts): ¿El prompt indica explícita e inequívocamente C#?
    *   Tipo de Aplicación (5 pts): ¿Especifica claramente una aplicación de consola?
    *   API Objetivo (10 pts): ¿Menciona explícitamente bluelytics.com.ar o una API similar y válida de Dólar Blue? ¿Está claro qué API usar si se sugieren varias?
    *   Bibliotecas Externas (10 pts): ¿Sugiere los paquetes NuGet necesarios (p.ej., para solicitudes HTTP, parseo JSON) o lo deja demasiado ambiguo para la IA?

*   B. Requisitos Funcionales y Claridad (50 puntos):
    *   Extracción de Datos (15 pts): ¿Solicita explícita y claramente el valor de compra (valor_compra), valor de venta (valor_venta) y fecha de última actualización (fecha_actualizacion)? ¿Son estos términos inequívocos?
    *   Salida JSON (20 pts): ¿EXIGE que la salida sea JSON? ¿O solo lo sugiere? ¿Cuán clara es la instrucción para una *única* salida JSON para los tres valores?
    *   Estructura JSON Implícita (15 pts): ¿Qué tan bien guía el prompt a la IA para crear una estructura JSON *lógica* (p.ej., un objeto raíz con claves como "compra", "venta", "fecha") sin definir explícitamente las claves y estructura JSON? Los prompts demasiado vagos aquí pierden puntos.

*   C. Robustez y Calidad del Prompt (20 puntos):
    *   Reducción de Ambigüedad (10 pts): ¿Qué tan bien minimiza el prompt la ambigüedad? ¿Considera problemas potenciales como límites de tasa de API (incluso si no pide implementar el manejo), errores de red o respuestas inesperadas de la API? (No necesita pedir código para manejar esto, pero ¿debería el prompt estar formulado para evitar la generación de código ingenuo?)
    *   Concisión y Efectividad (10 pts): ¿Es el prompt directo, inequívoco y libre de jerga que pueda confundir a la IA? ¿Es demasiado verboso o demasiado breve?

Basado en este análisis estricto, proporciona una puntuación (0-100) y un feedback detallado EN ESPAÑOL sobre las fortalezas, debilidades y áreas específicas de mejora del prompt.

Prompt a evaluar: {{{prompt}}}

Proporciona la puntuación (0-100) como "score", y el análisis (en ESPAÑOL) como "evaluationDetails".
`,
});

const evaluatePromptTechnicalFlow = ai.defineFlow(
  {
    name: 'evaluatePromptTechnicalFlow',
    inputSchema: EvaluatePromptTechnicalInputSchema,
    outputSchema: EvaluatePromptTechnicalOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);

