
'use server';
/**
 * @fileOverview This file contains the Genkit flow for scoring and providing feedback on a Rick and Morty API prompt.
 *
 * The flow evaluates the prompt's effectiveness in extracting specific character information
 * from the Rick and Morty API. It assesses the prompt based on clarity, completeness,
 * specificity, and how well it guides the AI to the desired output.
 *
 * @exports {function} scoreRickMortyPrompt - The main function to score and get feedback for the prompt.
 * @exports {type} ScoreRickMortyPromptInput - The input type for the scoring function.
 * @exports {type} ScoreRickMortyPromptOutput - The output type for the scoring function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

/**
 * Input schema for the Rick and Morty prompt scoring flow.
 */
const ScoreRickMortyPromptInputSchema = z.object({
  prompt: z
    .string()
    .describe('El prompt a evaluar para la extracción de información de personajes de Rick y Morty.'),
});
export type ScoreRickMortyPromptInput = z.infer<typeof ScoreRickMortyPromptInputSchema>;

/**
 * Output schema for the Rick and Morty prompt scoring flow.
 */
const ScoreRickMortyPromptOutputSchema = z.object({
  overallScore: z.number().describe('La puntuación general del prompt (sobre 100).'),
  feedback: z
    .string()
    .describe(
      'Feedback detallado en ESPAÑOL sobre el prompt, incluyendo fortalezas, debilidades y sugerencias de mejora.'
    ),
  clarity: z
    .number()
    .optional()
    .describe('Puntuación por claridad y especificidad del prompt (sobre 30).'),
  comprehensibility: z
    .number()
    .optional()
    .describe('Puntuación por facilidad de comprensión por una IA (sobre 25).'),
  organization: z
    .number()
    .optional()
    .describe('Puntuación por organización y estructura (sobre 25).'),
  detail: z
    .number()
    .optional()
    .describe('Puntuación por creatividad y detalle solicitado (sobre 20).'),
});
export type ScoreRickMortyPromptOutput = z.infer<typeof ScoreRickMortyPromptOutputSchema>;

/**
 * Scores a Rick and Morty API prompt and provides detailed feedback using Gemini AI.
 * @param {ScoreRickMortyPromptInput} input - The input containing the prompt to be scored.
 * @returns {Promise<ScoreRickMortyPromptOutput>} - The output containing the overall score and detailed feedback in Spanish.
 */
export async function scoreRickMortyPrompt(
  input: ScoreRickMortyPromptInput
): Promise<ScoreRickMortyPromptOutput> {
  return scoreRickMortyPromptFlow(input);
}

const scoreRickMortyPromptGenkitPrompt = ai.definePrompt({
  name: 'scoreRickMortyPromptGenkitPrompt',
  input: {schema: ScoreRickMortyPromptInputSchema},
  output: {schema: ScoreRickMortyPromptOutputSchema},
  prompt: `Eres un Experto en Ingeniería de Prompts de IA. Tu tarea es evaluar rigurosamente un prompt dado, diseñado para extraer información específica sobre personajes de la API de Rick y Morty (disponible en https://rickandmortyapi.com/api/character).

El prompt del usuario tiene como objetivo recuperar la siguiente información para los 5 personajes principales de la serie:
1.  Nombres
2.  Especies
3.  Origen (nombre del planeta/dimensión)
4.  Una breve descripción del personaje
5.  Estado (Vivo, Muerto o desconocido)
6.  Los nombres o IDs de los episodios en los que aparecen con más frecuencia (p.ej., los 3-5 episodios principales).

Evalúa el prompt del usuario basándote en los siguientes criterios. Proporciona una "overallScore" (0-100) y un "feedback" detallado EN ESPAÑOL.

Criterios de Evaluación:
1.  **Claridad y Especificidad (30 puntos):**
    *   ¿Cuán claramente establece el prompt el objetivo?
    *   ¿Menciona explícitamente los "5 personajes principales"? ¿O es ambiguo?
    *   ¿Lista con precisión todos los puntos de datos requeridos (nombre, especie, origen, descripción, estado, episodios frecuentes)?
    *   ¿Hay términos o solicitudes vagas?

2.  **Guía y Efectividad (40 puntos):**
    *   ¿Cuán efectivamente guía el prompt a una IA para consultar la API de Rick y Morty?
    *   ¿Sugiere implícita o explícitamente cómo identificar a los "personajes principales" (p.ej., por nombre, por número de apariciones, o lo deja demasiado abierto)?
    *   ¿Guía a la IA hacia una salida estructurada para cada personaje?
    *   ¿Qué tan probable es que el prompt produzca información precisa y completa para los 6 puntos de datos para 5 personajes?

3.  **Concisión y Formato (15 puntos):**
    *   ¿Es el prompt conciso y directo al grano?
    *   ¿Está libre de jerga innecesaria o fraseo demasiado complejo?
    *   ¿Está el prompt bien formateado y es fácil de analizar para una IA?

4.  **Manejo de Ambigüedad/Casos Límite (15 puntos):**
    *   ¿Considera el prompt ambigüedades potenciales (p.ej., ¿qué pasa si el "origen" es desconocido? ¿Qué pasa si un personaje tiene múltiples orígenes listados?)
    *   ¿Guía a la IA sobre cómo manejar casos donde la información podría faltar o no estar clara en la API para alguno de los puntos de datos de un personaje principal?

Basado en este análisis estricto, proporciona una "overallScore" (0-100) y un "feedback" detallado (EN ESPAÑOL) sobre las fortalezas, debilidades y sugerencias específicas y accionables para mejorar el prompt. Sé crítico y constructivo.

Prompt del Usuario:
{{{prompt}}}

Emite ÚNICAMENTE el overallScore (0-100) y el feedback detallado (en ESPAÑOL).
`,
});

const scoreRickMortyPromptFlow = ai.defineFlow(
  {
    name: 'scoreRickMortyPromptFlow',
    inputSchema: ScoreRickMortyPromptInputSchema,
    outputSchema: ScoreRickMortyPromptOutputSchema,
  },
  async input => {
    const {output} = await scoreRickMortyPromptGenkitPrompt(input);
    return {
        overallScore: output!.overallScore,
        feedback: output!.feedback,
        clarity: output!.clarity,
        comprehensibility: output!.comprehensibility,
        organization: output!.organization,
        detail: output!.detail,
    };
  }
);
