
"use client";
import { useEffect, useState, useRef } from 'react';
import type { ForwardedRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useGame } from '@/context/GameContext';
import { challenges, type Challenge as ChallengeData } from '@/lib/challengesData';
import { ChallengeDisplay } from '@/components/game/ChallengeDisplay';
import { TechnicalPromptChallenge, type TechnicalPromptChallengeHandle } from '@/components/challenges/TechnicalPromptChallenge';
import { RickAndMortyChallenge, type RickAndMortyChallengeHandle } from '@/components/challenges/RickAndMortyChallenge';
import { HumanOrAiChallengeView, type HumanOrAiChallengeViewHandle } from '@/components/challenges/HumanOrAiChallenge';
import { HallucinationHunterChallengeView, type HallucinationHunterChallengeViewHandle } from '@/components/challenges/HallucinationHunterChallenge';
import { BestPromptChallengeView, type BestPromptChallengeViewHandle } from '@/components/challenges/BestPromptChallenge';
import { Loader2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import type { EvaluatePromptTechnicalOutput } from '@/ai/flows/evaluate-prompt-technical';
import type { ScoreRickMortyPromptOutput } from '@/ai/flows/score-rick-morty-prompt';

type ChallengeComponentHandle = 
  | TechnicalPromptChallengeHandle
  | RickAndMortyChallengeHandle
  | HumanOrAiChallengeViewHandle
  | HallucinationHunterChallengeViewHandle
  | BestPromptChallengeViewHandle;

export default function ChallengePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { 
    submitAnswer, 
    nextChallenge, 
    getCurrentChallenge,
    gameStarted,
    gameOver
  } = useGame();
  
  const [challengeData, setChallengeData] = useState<ChallengeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitted, setIsSubmitted] = useState(false); 
  const [canProceedOverride, setCanProceedOverride] = useState(false);

  const challengeComponentRef = useRef<ChallengeComponentHandle>(null);


  useEffect(() => {
    // console.log("ChallengePage useEffect: Running. gameStarted:", gameStarted, "gameOver:", gameOver, "challengeId:", params.challengeId);
    if (!gameStarted && !gameOver) { 
      // console.log("ChallengePage useEffect: Not started and not over. Redirecting to /register.");
      router.push('/register');
      return;
    }
    if (gameOver) {
      console.log("ChallengePage useEffect: Game is over. Redirecting to /results.");
      router.push('/results');
      return;
    }

    const challengeIdParam = params.challengeId as string;
    const challengeId = challengeIdParam && /^\d+$/.test(challengeIdParam) ? parseInt(challengeIdParam) : NaN;
    
    if (isNaN(challengeId)) {
        // console.log("ChallengePage useEffect: Invalid challenge ID.", challengeIdParam);
        toast({ title: "Error", description: "ID de desafío inválido.", variant: "destructive" });
        router.push('/results'); // Or home if results also needs gameOver
        return;
    }

    const currentChallengeData = challenges.find(c => c.id === challengeId);
    
    if (currentChallengeData) {
      const contextChallenge = getCurrentChallenge(); // Relies on currentChallengeIndex from context
      if (contextChallenge && contextChallenge.id === currentChallengeData.id) {
        // console.log("ChallengePage useEffect: Challenge ID matches context. Setting challenge data.");
        setChallengeData(currentChallengeData);
      } else if (contextChallenge) { // Mismatch, redirect to where context says we should be
        // console.log("ChallengePage useEffect: Challenge ID mismatch. Context says:", contextChallenge.id, "URL says:", currentChallengeData.id, ". Redirecting.");
        router.replace(`/challenge/${contextChallenge.id}`);
        return; // Prevent further processing with potentially stale data
      } else { // No challenge in context (e.g., direct navigation or game just started)
         if (challenges.length > 0 && challenges[0].id === challengeId) {
            // console.log("ChallengePage useEffect: No context challenge, but URL matches first challenge. Setting data.");
            setChallengeData(currentChallengeData);
         } else {
            // console.log("ChallengePage useEffect: Sync error or unexpected state. Redirecting to /register.");
            toast({ title: "Error de Sincronización", description: "Reanudando desde el inicio del juego.", variant: "default" });
            router.push('/register'); 
            return;
         }
      }
    } else {
      // console.log("ChallengePage useEffect: Challenge data not found for ID:", challengeId);
      toast({ title: "Error", description: "Desafío no encontrado.", variant: "destructive" });
      router.push('/results'); 
      return;
    }
    // console.log("ChallengePage useEffect: Setup complete for challenge", challengeId);
    setIsLoading(false);
    setIsSubmitted(false); 
    setCanProceedOverride(false);
  }, [params.challengeId, gameStarted, gameOver, getCurrentChallenge, router, toast]);


  const handleTimeUp = () => {
    toast({
      title: "¡Tiempo Agotado!",
      description: "Pasando al siguiente desafío o resultados. Cualquier progreso no guardado podría perderse.",
      variant: "destructive",
      duration: 5000,
    });
    if (challengeData && !isSubmitted) {
        submitAnswer(challengeData.id, "TIME_UP_NO_SUBMISSION", 0);
        setIsSubmitted(true); 
    }
    setCanProceedOverride(true); 
    setTimeout(() => {
        nextChallenge();
    }, 1000);
  };

  const handleChallengeSubmit = (
    value: any, 
    score: number, 
    feedback?: { technical?: EvaluatePromptTechnicalOutput, rickMorty?: ScoreRickMortyPromptOutput }
  ) => {
    if (challengeData) {
      submitAnswer(challengeData.id, value, score, feedback);
      setIsSubmitted(true); 
    }
  };
  
  const handleProceed = async () => {
    // console.log("ChallengePage handleProceed: Clicked. gameOver:", gameOver);
    if (gameOver) {
        router.push('/results');
        return;
    }

    if (challengeData && !isSubmitted && !canProceedOverride) {
      let submissionAttemptSuccessful = false;
      if (challengeComponentRef.current && typeof challengeComponentRef.current.triggerInternalSubmit === 'function') {
        // console.log("ChallengePage handleProceed: Attempting internal submit for challenge", challengeData.id);
        try {
          submissionAttemptSuccessful = await challengeComponentRef.current.triggerInternalSubmit();
          // console.log("ChallengePage handleProceed: Internal submit result:", submissionAttemptSuccessful);
        } catch (error) {
          console.error("Error al intentar envío automático:", error);
          toast({
            title: "Error en Envío Automático",
            description: "Hubo un problema al intentar enviar tu respuesta. Se registrará 0 puntos.",
            variant: "destructive",
          });
        }
      }

      if (!submissionAttemptSuccessful) {
        // console.log("ChallengePage handleProceed: Internal submit failed or not applicable. Submitting 0 score.");
        if (!challengeComponentRef.current?.triggerInternalSubmit) {
             toast({
                title: "Desafío Omitido",
                description: "Avanzando sin completar el desafío. Se registrará una puntuación de 0.",
                variant: "default",
                duration: 3000,
            });
        }
        submitAnswer(challengeData.id, "SKIPPED_OR_AUTO_FAILED", 0);
        setIsSubmitted(true); 
      }
    }
    
    // console.log("ChallengePage handleProceed: Calling nextChallenge().");
    nextChallenge();
  };


  if (isLoading || !challengeData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <p className="mt-4 text-2xl text-accent">Cargando Desafío...</p>
      </div>
    );
  }

  const renderChallengeContent = () => {
    const commonProps = {
      challenge: challengeData,
      isSubmitted: isSubmitted,
    };

    switch (challengeData.type) {
      case 'prompt-evaluation':
        return <TechnicalPromptChallenge ref={challengeComponentRef as ForwardedRef<TechnicalPromptChallengeHandle>} {...commonProps} onSubmit={handleChallengeSubmit} />;
      case 'prompt':
        return <RickAndMortyChallenge ref={challengeComponentRef as ForwardedRef<RickAndMortyChallengeHandle>} {...commonProps} onSubmit={handleChallengeSubmit} />;
      case 'human-ai':
        return <HumanOrAiChallengeView ref={challengeComponentRef as ForwardedRef<HumanOrAiChallengeViewHandle>} {...commonProps} onComplete={handleChallengeSubmit} />;
      case 'errors':
        return <HallucinationHunterChallengeView ref={challengeComponentRef as ForwardedRef<HallucinationHunterChallengeViewHandle>} {...commonProps} onComplete={handleChallengeSubmit} />;
      case 'prompt-choice':
        return <BestPromptChallengeView ref={challengeComponentRef as ForwardedRef<BestPromptChallengeViewHandle>} {...commonProps} onComplete={handleChallengeSubmit} />;
      default:
        return <div>Tipo de desafío no soportado.</div>;
    }
  };

  return (
    <ChallengeDisplay
      challenge={challengeData}
      onTimeUp={handleTimeUp}
      onNext={handleProceed}
    >
      {renderChallengeContent()}
    </ChallengeDisplay>
  );
}
