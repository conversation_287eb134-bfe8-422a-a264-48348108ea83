import type { ReactNode } from 'react';
import { GameProvider } from '@/context/GameContext';
import { Header } from '@/components/game/Header';

export default function GameLayout({ children }: { children: ReactNode }) {
  return (
    <GameProvider>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow container mx-auto px-4 py-8">
          {children}
        </main>
        <footer className="text-center p-4 text-muted-foreground text-sm">
          Prompt Battle - Arena
        </footer>
      </div>
    </GameProvider>
  );
}
