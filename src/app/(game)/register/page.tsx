
'use client';
import { useState, type FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useGame } from '@/context/GameContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserPlus } from 'lucide-react';

export default function RegisterPage() {
  const [name, setName] = useState('');
  const { setPlayerName } = useGame();
  const router = useRouter();

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      setPlayerName(name.trim());
      router.push('/waiting-room');
    }
  };

  return (
    <div className="flex items-center justify-center py-12">
      <Card className="w-full max-w-md neon-border">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-headline neon-glow-primary flex items-center justify-center">
            <UserPlus className="mr-3 h-10 w-10 text-primary" />
            Únete a la Arena
          </CardTitle>
          <CardDescription className="text-accent text-lg">
            ¡Ingresa tu nombre de dev para comenzar la batalla!
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="playerName" className="text-xl text-primary">
                Nombre del Dev
              </Label>
              <Input
                id="playerName"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Ej: PromptMasterFlex"
                required
                className="arcade-input text-xl"
                maxLength={20}
              />
            </div>
            <Button type="submit" className="w-full arcade-button text-xl" disabled={!name.trim()}>
              Entrar a la Sala de Espera
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
