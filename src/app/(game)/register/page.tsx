
'use client';
import { useState, type FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useGame } from '@/context/GameContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserPlus } from 'lucide-react';

export default function RegisterPage() {
  const [name, setName] = useState('');
  const { setPlayerName } = useGame();
  const router = useRouter();

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      setPlayerName(name.trim());
      router.push('/waiting-room');
    }
  };

  return (
    <div className="flex items-center justify-center py-12 relative">
      {/* Background effects */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/3 left-1/3 w-64 h-64 bg-purple-500/30 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-64 h-64 bg-cyan-500/30 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1.5s'}}></div>
      </div>
      
      <Card className="w-full max-w-md hotline-miami-card relative z-10 animate-hotline-glow">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-cyan-500/5 rounded-lg blur-sm"></div>
        <CardHeader className="text-center relative z-10">
          <CardTitle className="text-4xl font-headline neon-text-readable flex items-center justify-center animate-neon-pulse">
            <UserPlus className="mr-3 h-10 w-10 text-primary animate-bounce" />
            ÚNETE A LA ARENA
          </CardTitle>
          <CardDescription className="text-lg neon-text-accent-readable">
            ¡Ingresa tu nombre de dev para comenzar la batalla!
          </CardDescription>
        </CardHeader>
        <CardContent className="relative z-10">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="playerName" className="text-xl text-primary neon-glow-primary">
                NOMBRE DEL DEV
              </Label>
              <Input
                id="playerName"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Ej: PromptMasterFlex"
                required
                className="arcade-input text-xl"
                maxLength={20}
              />
            </div>
            <Button type="submit" className="w-full arcade-button text-xl" disabled={!name.trim()}>
              <span className="relative z-10">ENTRAR A LA SALA DE ESPERA</span>
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
