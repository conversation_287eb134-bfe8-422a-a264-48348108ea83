
"use client";
import { useEffect, useState, useRef } from 'react';
import { useGame, type Answer, type Player } from '@/context/GameContext';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from '@/components/ui/card';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Trophy, ArrowLeftCircle, CheckSquare, BarChart3, MessageSquare, Edit3, Bot, Brain, Search, CheckCircle, XCircle, LightbulbIcon, Users, Loader2 } from 'lucide-react';
import { challenges, type Challenge as ChallengeData } from '@/lib/challengesData';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { redis } from '@/lib/upstash';

interface PlayerRank extends Player { 
  rank: number;
}

interface LeaderboardEntry {
  id: string; // For React key, can be playerName for Upstash if unique
  playerName: string;
  score: number;
}

export default function ResultsPage() {
  const { players, answers, playerName, resetGame, gameOver, gameStarted, currentScore, currentChallengeIndex } = useGame(); 
  const router = useRouter();
  const { toast } = useToast();
  
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [leaderboardLoading, setLeaderboardLoading] = useState(false);

  const localRankedPlayers: PlayerRank[] = [...players]
    .sort((a, b) => b.score - a.score)
    .map((player, index) => ({ ...player, rank: index + 1 }));

  const currentPlayerLocalRank = localRankedPlayers.find(p => p.name === playerName);

  useEffect(() => {
     if (!gameStarted && !gameOver) {
       router.push('/'); 
     }
  }, [gameStarted, gameOver, router]);

  // Use a ref to track if we've already loaded the leaderboard
  const leaderboardLoadedRef = useRef(false);

  const fetchLeaderboard = async () => {
    if (leaderboardLoading || leaderboardLoadedRef.current) return;

    console.log("ResultsPage: fetchLeaderboard called. Checking Upstash redis client...");
    if (!redis) {
      console.warn("ResultsPage: Upstash Redis client not available");
      setLeaderboardLoading(false);
      return;
    }

    if (typeof redis.zrange !== 'function') {
      console.error("ResultsPage: redis.zrange is not a function");
      setLeaderboardLoading(false);
      return;
    }

    setLeaderboardLoading(true);
    try {
      const redisData = await redis.zrange('leaderboard', 0, 9, { withScores: true, rev: true });

      const entries: LeaderboardEntry[] = [];
      for (let i = 0; i < redisData.length; i += 2) {
        entries.push({
          id: redisData[i] as string,
          playerName: redisData[i] as string,
          score: Number(redisData[i + 1]),
        });
      }
      setLeaderboard(entries);
      leaderboardLoadedRef.current = true;
    } catch (error) {
      console.error("Error fetching global leaderboard from Upstash:", error);
    } finally {
      setLeaderboardLoading(false);
    }
  };

  useEffect(() => {
    if (gameOver && !leaderboardLoadedRef.current) {
      fetchLeaderboard();
    }
  }, [gameOver]);


  const getCurrentChallengeContext = () => {
    if (gameStarted && !gameOver && currentChallengeIndex >= 0 && currentChallengeIndex < challenges.length) {
      return challenges[currentChallengeIndex];
    }
    return null;
  };
  const currentContextChallenge = getCurrentChallengeContext();


  if (!gameOver && gameStarted) {
    return (
        <div className="text-center py-10">
            <h2 className="text-2xl text-accent">Juego en Progreso</h2>
            <p className="text-muted-foreground">Termina todos los desafíos para ver tus resultados.</p>
            {challenges.length > 0 && currentContextChallenge && (
                 <Button onClick={() => router.push(`/challenge/${currentContextChallenge!.id}`)} className="mt-4 arcade-button">
                    Volver al Juego
                 </Button>
            )}
            {challenges.length > 0 && !currentContextChallenge && ( 
                 <Button onClick={() => router.push(`/challenge/${challenges[0].id}`)} className="mt-4 arcade-button">
                    Reanudar Juego
                 </Button>
            )}
        </div>
    );
  }
  
  if (!playerName && !gameOver) { 
    return (
        <div className="text-center py-10">
            <h2 className="text-2xl text-accent">Sin Datos del Juego</h2>
            <p className="text-muted-foreground">Comienza un nuevo juego para ver los resultados.</p>
            <Button onClick={() => router.push('/')} className="mt-4 arcade-button">
                Ir al Inicio
            </Button>
        </div>
    );
  }

  const renderAnswerValue = (answer: Answer, challenge: ChallengeData | undefined) => {
    if (!challenge) return <p>Respuesta no disponible.</p>;

    if (answer.value === "TIME_UP_NO_SUBMISSION") return <p className="text-destructive">Tiempo agotado, sin respuesta.</p>;
    if (answer.value === "SKIPPED_OR_AUTO_FAILED") return <p className="text-yellow-400">Desafío omitido o envío automático fallido.</p>;
    
    switch (challenge.type) {
        case 'prompt-evaluation':
        case 'prompt':
            return <p className="font-mono bg-black/20 p-2 rounded-md whitespace-pre-wrap text-sm">{answer.value || "Prompt no enviado"}</p>;
        case 'human-ai':
            const typedChallengeHA = challenge as Extract<ChallengeData, { type: 'human-ai' }>;
            const answerValueHA = answer.value as Record<string, string> || {};
            return (
                <ul className="space-y-1 text-sm">
                {typedChallengeHA.snippets.map(snippet => (
                    <li key={snippet.id}>
                        Fragmento #{typedChallengeHA.snippets.indexOf(snippet) + 1}: <span className="font-semibold">{answerValueHA[snippet.id]?.toUpperCase() || "No respondido"}</span> (Correcto: {snippet.isHuman ? "HUMANO" : "IA"})
                    </li>
                ))}
                </ul>
            );
        case 'errors':
             const typedChallengeE = challenge as Extract<ChallengeData, { type: 'errors' }>;
             const selectedErrorIds = (answer.value as string[]) || [];
             if (selectedErrorIds.length === 0) return <p>No se marcaron errores.</p>;
             return <p className="text-sm">IDs de errores marcados: {selectedErrorIds.join(', ')}</p>;

        case 'prompt-choice':
            const typedChallengePC = challenge as Extract<ChallengeData, { type: 'prompt-choice' }>;
            const answerValuePC = answer.value as Record<string, string> || {};
            return (
                 <ul className="space-y-1 text-sm">
                {typedChallengePC.scenarios.map(scenario => (
                    <li key={scenario.id}>
                        Escenario "{scenario.taskDescription.substring(0,30)}...": Opción <span className="font-semibold">{answerValuePC[scenario.id] || "No respondido"}</span> (Correcta: {scenario.correctOptionId})
                    </li>
                ))}
                </ul>
            );
        default:
            return <p className="text-sm">{JSON.stringify(answer.value)}</p>;
    }
  }


  return (
    <div className="py-12 space-y-8">
      <Card className="w-full max-w-3xl mx-auto neon-border-accent shadow-neon-accent">
        <CardHeader className="text-center">
          <Trophy className="mx-auto h-20 w-20 text-yellow-400 neon-glow-primary animate-pulse" />
          <CardTitle className="text-5xl font-headline neon-glow-primary mt-4">
            ¡Juego Terminado!
          </CardTitle>
          <CardDescription className="text-accent text-2xl">
            {currentPlayerLocalRank ? `¡Felicidades, ${currentPlayerLocalRank.name}!` : "Aquí están los resultados:"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {currentPlayerLocalRank && (
            <div className="text-center p-6 bg-primary/20 rounded-lg">
              <p className="text-2xl">Tu Rango (Local): <span className="font-bold text-primary neon-glow-primary">#{currentPlayerLocalRank.rank}</span></p>
              <p className="text-4xl mt-2">Puntuación Total: <span className="font-bold text-primary neon-glow-primary">{currentScore}</span></p>
            </div>
          )}

          <div>
            <h3 className="text-2xl font-semibold text-accent mb-3 flex items-center"><BarChart3 className="mr-2"/>Clasificación (Esta Partida):</h3>
            {localRankedPlayers.length > 0 ? (
                <ul className="space-y-2">
                {localRankedPlayers.map((player) => (
                    <li key={player.id} className={`flex justify-between items-center p-3 rounded-md text-lg ${player.name === playerName ? 'bg-primary/30 text-primary-foreground font-bold neon-glow-primary' : 'bg-card'}`}>
                    <span>#{player.rank}. {player.name}</span>
                    <span className="font-bold">{player.score} pts</span>
                    </li>
                ))}
                </ul>
            ) : (
                <p className="text-muted-foreground">No hay datos de jugadores para la clasificación local.</p>
            )}
          </div>

           <div className="mt-8">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-2xl font-semibold text-accent flex items-center"><Users className="mr-2"/>Marcador Global (Top 10):</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setLeaderboard([]);
                  leaderboardLoadedRef.current = false;
                  fetchLeaderboard();
                }}
                disabled={leaderboardLoading}
                className="text-xs"
              >
                {leaderboardLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Actualizar"}
              </Button>
            </div>
            {leaderboardLoading ? (
                <div className="flex justify-center items-center py-6">
                    <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    <p className="ml-4 text-accent">Cargando marcador global...</p>
                </div>
            ) : leaderboard.length > 0 ? (
                <ul className="space-y-2">
                {leaderboard.map((entry, index) => (
                    <li key={entry.id} className={`flex justify-between items-center p-3 rounded-md text-lg ${entry.playerName === playerName && entry.score === currentScore ? 'bg-accent/30 text-accent-foreground font-bold neon-glow-accent' : 'bg-card'}`}>
                    <span>#{index + 1}. {entry.playerName}</span>
                    <span className="font-bold">{entry.score} pts</span>
                    </li>
                ))}
                </ul>
            ) : (
                 <p className="text-muted-foreground text-center py-4">
                    No hay puntuaciones en el marcador global aún, ¡o no se pudo cargar!
                    { (process.env.NEXT_PUBLIC_UPSTASH_REDIS_REST_URL && process.env.NEXT_PUBLIC_UPSTASH_REDIS_REST_TOKEN) ? " Sé el primero en aparecer." : " (Verifica la configuración de Upstash y las variables de entorno)."}
                </p>
            )}
          </div>


          <div className="mt-8">
            <h3 className="text-2xl font-semibold text-accent mb-3 flex items-center"><MessageSquare className="mr-2"/>Revisión Detallada de Desafíos:</h3>
            {answers.length > 0 ? (
                <Accordion type="multiple" className="w-full space-y-3">
                 {answers.map((ans, index) => {
                   const challenge = challenges.find(c => c.id === ans.challengeId);
                   let icon = <Edit3 className="mr-2 h-5 w-5 text-primary"/>;
                   if (challenge?.type === 'human-ai') icon = <><Brain className="mr-1 h-5 w-5 text-green-400"/><Bot className="mr-2 h-5 w-5 text-blue-400"/></>;
                   if (challenge?.type === 'errors') icon = <Search className="mr-2 h-5 w-5 text-yellow-400"/>;
                   if (challenge?.type === 'prompt-choice') icon = <CheckSquare className="mr-2 h-5 w-5 text-indigo-400"/>;
                   
                   const isCorrectGeneral = ans.score > 0; 

                   return (
                     <AccordionItem key={index} value={`item-${index}`} className={`bg-card/70 rounded-lg border-l-4 ${isCorrectGeneral ? 'border-green-500' : 'border-destructive'}`}>
                       <AccordionTrigger className={`p-4 text-lg hover:no-underline ${isCorrectGeneral ? 'hover:bg-green-500/10' : 'hover:bg-destructive/10'}`}>
                         <div className="flex items-center text-left">
                           {icon} {challenge?.title || `Desafío ${ans.challengeId}`}
                           {isCorrectGeneral ? <CheckCircle className="ml-auto h-5 w-5 text-green-400" /> : <XCircle className="ml-auto h-5 w-5 text-destructive" />}
                         </div>
                       </AccordionTrigger>
                       <AccordionContent className="p-4 space-y-3">
                         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 className="font-semibold text-primary text-md mb-1">Tu Respuesta:</h4>
                                {renderAnswerValue(ans, challenge)}
                            </div>
                            <div>
                                <h4 className="font-semibold text-primary text-md mb-1">Tu Puntuación:</h4>
                                <p className="text-xl font-bold text-accent neon-glow-accent">{ans.score}</p>
                            </div>
                         </div>
                         
                         {challenge?.type === 'prompt-evaluation' && ans.technicalPromptFeedback && (
                            <div className="mt-3 pt-3 border-t border-primary/30">
                                <h4 className="font-semibold text-primary text-md mb-1 flex items-center"><LightbulbIcon className="mr-2 h-5 w-5"/>Feedback de IA (Prompt Técnico):</h4>
                                <p className="whitespace-pre-wrap p-2 bg-black/30 rounded text-sm">{ans.technicalPromptFeedback.evaluationDetails}</p>
                            </div>
                         )}
                         {challenge?.type === 'prompt' && ans.rickAndMortyFeedback && (
                            <div className="mt-3 pt-3 border-t border-primary/30">
                                <h4 className="font-semibold text-primary text-md mb-1 flex items-center"><LightbulbIcon className="mr-2 h-5 w-5"/>Feedback de IA (Rick & Morty):</h4>
                                <p className="whitespace-pre-wrap p-2 bg-black/30 rounded text-sm">{ans.rickAndMortyFeedback.feedback}</p>
                                { (ans.rickAndMortyFeedback.clarity !== undefined || ans.rickAndMortyFeedback.comprehensibility !== undefined || ans.rickAndMortyFeedback.organization !== undefined || ans.rickAndMortyFeedback.detail !== undefined) && (
                                     <details className="bg-black/20 p-2 rounded-md mt-2 text-xs">
                                        <summary className="text-muted-foreground cursor-pointer hover:text-accent">Ver desglose de puntuación (si disponible)</summary>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-0.5 mt-1">
                                            {ans.rickAndMortyFeedback.clarity !== undefined && <div><strong className="text-primary/80">Claridad:</strong> {ans.rickAndMortyFeedback.clarity}/30</div>}
                                            {ans.rickAndMortyFeedback.comprehensibility !== undefined && <div><strong className="text-primary/80">Comprensibilidad:</strong> {ans.rickAndMortyFeedback.comprehensibility}/25</div>}
                                            {ans.rickAndMortyFeedback.organization !== undefined && <div><strong className="text-primary/80">Organización:</strong> {ans.rickAndMortyFeedback.organization}/25</div>}
                                            {ans.rickAndMortyFeedback.detail !== undefined && <div><strong className="text-primary/80">Detalle:</strong> {ans.rickAndMortyFeedback.detail}/20</div>}
                                        </div>
                                     </details>
                                )}
                            </div>
                         )}
                       </AccordionContent>
                     </AccordionItem>
                   );
                 })}
               </Accordion>
            ) : (
                <p className="text-muted-foreground">No se registraron respuestas para este juego.</p>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={resetGame} className="arcade-button-accent text-xl">
            <ArrowLeftCircle className="mr-2 h-6 w-6" /> Jugar de Nuevo
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
