
'use client';
import { useRouter } from 'next/navigation';
import { useGame } from '@/context/GameContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Play, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { challenges } from '@/lib/challengesData'; // Moved and changed to ES6 import

export default function WaitingRoomPage() {
  const { playerName, startGame, gameStarted } = useGame();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!playerName) {
      router.push('/register');
    }
  }, [playerName, router]);
  
  useEffect(() => {
    if (gameStarted) {
       if (challenges && challenges.length > 0) {
        router.push(`/challenge/${challenges[0].id}`); 
       } else {
        // Fallback or error handling if challenges are not loaded
        console.error("Challenges not loaded, cannot redirect.");
        router.push('/results'); // Or some other safe page
       }
    }
  }, [gameStarted, router]);


  const handleStartGame = () => {
    setIsLoading(true);
    startGame(); 
    // El enrutamiento al primer desafío se maneja en el useEffect de gameStarted
  };

  if (!playerName) {
    return (
      <div className="text-center py-10">
        <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
        <p className="text-xl text-accent">Cargando datos del dev...</p>
        <p className="mt-2 text-sm text-muted-foreground">Si no eres redirigido, por favor <Link href="/register" className="underline hover:text-primary">regístrate</Link>.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12">
      <Card className="w-full max-w-lg neon-border">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-headline neon-glow-primary flex items-center justify-center">
            <Users className="mr-3 h-10 w-10 text-primary" />
            Sala de Espera
          </CardTitle>
          <CardDescription className="text-accent text-lg">
            ¡La batalla de prompts está por comenzar!
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-2xl font-semibold text-primary mb-3">Devs Conectados:</h3>
            <ul className="space-y-2">
              {playerName && (
                <li className="p-3 rounded-md text-lg bg-primary/30 text-primary-foreground font-bold neon-glow-primary text-opacity-100">
                  {playerName} (Tú)
                </li>
              )}
            </ul>
          </div>
          <Button 
            onClick={handleStartGame} 
            className="w-full arcade-button-accent text-xl flex items-center justify-center"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-6 w-6 animate-spin" /> Iniciando...
              </>
            ) : (
              <>
                <Play className="mr-2 h-6 w-6" /> Iniciar Juego
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
