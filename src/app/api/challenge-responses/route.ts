import { NextRequest, NextResponse } from 'next/server';
import { saveChallengeResponse, getPlayerResponses, getChallengeResponses } from '@/lib/challengeResponses';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { playerName, challengeId, response, score, feedback } = body;

    if (!playerName || challengeId === undefined || response === undefined || score === undefined) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: playerName, challengeId, response, score' 
      }, { status: 400 });
    }

    const success = await saveChallengeResponse(playerName, challengeId, response, score, feedback);

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Challenge response saved successfully' 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to save challenge response' 
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error in challenge-responses POST:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const playerName = searchParams.get('playerName');
    const challengeId = searchParams.get('challengeId');

    if (playerName) {
      // Get responses for a specific player
      const responses = await getPlayerResponses(playerName);
      return NextResponse.json({ 
        success: true, 
        responses 
      });
    } else if (challengeId) {
      // Get responses for a specific challenge
      const responses = await getChallengeResponses(parseInt(challengeId));
      return NextResponse.json({ 
        success: true, 
        responses 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'Either playerName or challengeId parameter is required' 
      }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Error in challenge-responses GET:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}
