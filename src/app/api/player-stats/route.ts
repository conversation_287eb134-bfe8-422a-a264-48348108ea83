import { NextRequest, NextResponse } from 'next/server';
import { getPlayerStats, getTopPlayers } from '@/lib/challengeResponses';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const playerName = searchParams.get('playerName');
    const top = searchParams.get('top');

    if (playerName) {
      // Get stats for a specific player
      const stats = await getPlayerStats(playerName);
      if (stats) {
        return NextResponse.json({ 
          success: true, 
          stats 
        });
      } else {
        return NextResponse.json({ 
          success: false, 
          error: 'Player stats not found' 
        }, { status: 404 });
      }
    } else if (top) {
      // Get top players
      const limit = parseInt(top) || 10;
      const topPlayers = await getTopPlayers(limit);
      return NextResponse.json({ 
        success: true, 
        topPlayers 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'Either playerName or top parameter is required' 
      }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Error in player-stats GET:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}
