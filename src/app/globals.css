@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 13%; /* #222222 Dark Grey */
    --foreground: 0 0% 95%; /* #F2F2F2 Light Grey/Off-white for general text */

    --card: 0 0% 18%; /* #2E2E2E Slightly lighter grey */
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 18%;
    --popover-foreground: 0 0% 95%;

    --primary: 300 100% 50%; /* #FF00FF Vibrant Magenta */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 183 80% 45%; /* A slightly darker Electric Blue for secondary elements */
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 25%; /* Darker muted color */
    --muted-foreground: 0 0% 65%; /* Lighter muted foreground */

    --accent: 183 100% 71%; /* #7DF9FF Electric Blue */
    --accent-foreground: 0 0% 7%; /* Dark color for text on accent, or white if contrast allows */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 300 100% 50%; /* Magenta borders for neon effect */
    --input: 0 0% 20%; /* Darker input background */
    --ring: 183 100% 71%; /* Electric Blue for focus rings */

    --radius: 0.5rem;

    /* Chart colors - can be adapted to the theme */
    --chart-1: 300 100% 60%; /* Lighter Magenta */
    --chart-2: 183 100% 60%; /* Lighter Electric Blue */
    --chart-3: 300 80% 40%;  /* Darker Magenta */
    --chart-4: 183 80% 40%;  /* Darker Electric Blue */
    --chart-5: 45 100% 50%; /* Neon Yellow/Gold for contrast */

    /* Sidebar (not heavily used, but themed) */
    --sidebar-background: 0 0% 10%;
    --sidebar-foreground: 0 0% 85%;
    --sidebar-primary: 300 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 183 100% 71%;
    --sidebar-accent-foreground: 0 0% 7%;
    --sidebar-border: 300 100% 50%;
    --sidebar-ring: 183 100% 71%;
  }

  .dark {
    /* Dark theme is the default, but if specific dark variations are needed, define here */
    /* For this project, :root variables are already dark-themed */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply font-body; /* Ensure Space Grotesk is applied */
  }
  /* Add a neon glow effect class */
  .neon-glow-primary {
    text-shadow:
      0 0 5px hsl(var(--primary) / 0.8),
      0 0 10px hsl(var(--primary) / 0.6),
      0 0 15px hsl(var(--primary) / 0.4),
      0 0 20px hsl(var(--primary) / 0.2);
  }
  .neon-glow-accent {
    text-shadow:
      0 0 5px hsl(var(--accent) / 0.8),
      0 0 10px hsl(var(--accent) / 0.6),
      0 0 15px hsl(var(--accent) / 0.4),
      0 0 20px hsl(var(--accent) / 0.2);
  }
  .neon-border {
    border-width: 2px;
    border-style: solid;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 5px hsl(var(--primary) / 0.8), 0 0 10px hsl(var(--primary) / 0.6);
  }
  .neon-border-accent {
    border-width: 2px;
    border-style: solid;
    border-color: hsl(var(--accent));
    box-shadow: 0 0 5px hsl(var(--accent) / 0.8), 0 0 10px hsl(var(--accent) / 0.6);
  }

  /* Custom scrollbar for arcade feel */
  ::-webkit-scrollbar {
    width: 10px;
  }
  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
    border-radius: var(--radius);
  }
  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: var(--radius);
    border: 2px solid hsl(var(--background));
  }
  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }
}

@layer components {
  .arcade-button {
    @apply px-6 py-3 bg-primary text-primary-foreground rounded-md font-headline uppercase tracking-wider transition-all duration-150 ease-in-out hover:bg-opacity-80 hover:shadow-[0_0_15px_hsl(var(--primary)),0_0_25px_hsl(var(--primary)/0.7)] active:scale-95;
  }
  .arcade-button-accent {
    @apply px-6 py-3 bg-accent text-background rounded-md font-headline uppercase tracking-wider transition-all duration-150 ease-in-out hover:bg-opacity-80 hover:shadow-[0_0_15px_hsl(var(--accent)),0_0_25px_hsl(var(--accent)/0.7)] active:scale-95;
  }
  .arcade-input {
    @apply bg-input border-2 border-primary focus:border-accent focus:ring-accent rounded-md p-3 placeholder-muted-foreground text-lg;
    box-shadow: inset 0 0 5px hsl(var(--primary)/0.5);
  }
  .challenge-card {
    @apply bg-card p-6 rounded-lg shadow-xl neon-border;
  }
}
