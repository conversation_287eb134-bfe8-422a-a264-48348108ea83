"use client";
import { useGame } from '@/context/GameContext';
import Leaderboard from '@/components/Leaderboard';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function LeaderboardPage() {
  const { playerName, currentScore } = useGame();
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="arcade-button"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver
          </Button>
          <h1 className="text-4xl font-headline text-center text-primary neon-glow-primary">
            Leaderboard Global
          </h1>
          <div className="w-24"></div> {/* Spacer for centering */}
        </div>
        
        <Leaderboard 
          currentPlayerName={playerName} 
          currentPlayerScore={currentScore} 
        />
      </div>
    </div>
  );
}
