import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Rocket, BrainCircuit } from 'lucide-react';
import BackgroundMusic from '@/components/BackgroundMusic';

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background text-foreground p-4">
      <div className="absolute top-4 right-4">
        <Link href="/leaderboard">
          <Button variant="outline">Ver Leaderboard</Button>
        </Link>
      </div>
      <main className="text-center space-y-8">
        <h1 className="text-6xl md:text-8xl font-headline neon-glow-primary animate-neon-flicker">
          Prompt Battle
        </h1>
        <p className="text-xl md:text-2xl text-accent max-w-2xl mx-auto">
          ¡Entra a la Arena y combate en desafíos de ingeniería de prompts en tiempo real! Demuestra tus habilidades, supera a la IA y escala en la clasificación.
        </p>
        <div className="space-y-4">
          <Link href="/register">
            <Button className="arcade-button text-2xl px-10 py-6 group">
              Entrar a la Arena <Rocket className="ml-2 h-6 w-6 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
          <p className="text-sm text-muted-foreground">
          </p>
        </div>
        <div className="mt-12 p-6 bg-card rounded-lg neon-border-accent max-w-md mx-auto">
            <h2 className="text-2xl font-headline text-primary mb-4 flex items-center justify-center">
                <BrainCircuit className="mr-2 h-7 w-7"/> Características
            </h2>
            <ul className="list-disc list-inside space-y-2 text-left text-lg">
                <li>5 Desafíos Únicos de IA y Prompts</li>
                <li>Juego en Tiempo Real</li>
                <li>Prueba tu Intuición en GenAI</li>
            </ul>
        </div>
      </main>
      <footer className="absolute bottom-4 text-muted-foreground text-sm">
      </footer>

      <BackgroundMusic src="/hotline-miami.mp3" volume={0.2} />
    </div>
  );
}
