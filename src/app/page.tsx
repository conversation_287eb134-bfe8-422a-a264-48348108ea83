import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Rocket, BrainCircuit } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-purple-900 via-black to-cyan-900 text-foreground p-4 relative overflow-hidden">
      {/* Animated background effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
      </div>
      
      {/* Scan lines effect */}
      <div className="absolute inset-0 scan-lines opacity-50"></div>
      
      <div className="absolute top-4 right-4">
        <Link href="/leaderboard">
          <Button variant="outline" className="border-2 border-accent text-accent hover:bg-accent hover:text-black transition-all duration-300">
            Ver Leaderboard
          </Button>
        </Link>
      </div>
      
      <main className="text-center space-y-8 relative z-10">
        <div className="relative">
          <h1 className="text-6xl md:text-8xl font-headline neon-glow-primary animate-neon-flicker relative">
            PROMPT BATTLE
          </h1>
          <div className="absolute inset-0 text-6xl md:text-8xl font-headline text-cyan-400/30 animate-pulse" style={{transform: 'translate(2px, 2px)'}}>
            PROMPT BATTLE
          </div>
        </div>
        
        <p className="text-xl md:text-2xl max-w-2xl mx-auto neon-text-accent-readable animate-pulse">
          ¡Entra a la Arena y combate en desafíos de ingeniería de prompts en tiempo real!
          <br />
          <span className="neon-text-readable">Demuestra tus habilidades, supera a la IA y escala en la clasificación.</span>
        </p>
        
        <div className="space-y-4">
          <Link href="/register">
            <Button className="arcade-button text-2xl px-10 py-6 group relative overflow-hidden">
              <span className="relative z-10">
                Entrar a la Arena <Rocket className="ml-2 h-6 w-6 transition-transform group-hover:translate-x-1 group-hover:rotate-12" />
              </span>
            </Button>
          </Link>
        </div>
        
        <div className="mt-12 p-6 hotline-miami-card rounded-lg max-w-md mx-auto relative animate-hotline-glow">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-cyan-500/10 rounded-lg blur-sm"></div>
          <div className="relative z-10">
            <h2 className="text-2xl font-headline text-primary mb-4 flex items-center justify-center neon-glow-primary">
              <BrainCircuit className="mr-2 h-7 w-7 animate-spin" style={{animationDuration: '3s'}} /> 
              CARACTERÍSTICAS
            </h2>
            <ul className="list-none space-y-3 text-left text-lg">
              <li className="flex items-center neon-text-accent-readable">
                <span className="w-2 h-2 bg-accent rounded-full mr-3 animate-pulse"></span>
                5 Desafíos Únicos de IA y Prompts
              </li>
              <li className="flex items-center neon-text-readable">
                <span className="w-2 h-2 bg-primary rounded-full mr-3 animate-pulse" style={{animationDelay: '0.5s'}}></span>
                Juego en Tiempo Real
              </li>
              <li className="flex items-center neon-text-accent-readable">
                <span className="w-2 h-2 bg-cyan-400 rounded-full mr-3 animate-pulse" style={{animationDelay: '1s'}}></span>
                Prueba tu Intuición en GenAI
              </li>
            </ul>
          </div>
        </div>
      </main>
      
      <footer className="absolute bottom-4 text-muted-foreground text-sm neon-glow-accent">
        © 2025 Prompt Battle Arena
      </footer>
    </div>
  );
}
