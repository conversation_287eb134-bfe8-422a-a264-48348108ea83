"use client";
import { useEffect, useRef, useState } from 'react';
import { Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BackgroundMusicProps {
  src: string;
  volume?: number;
}

export default function BackgroundMusic({ src, volume = 0.3 }: BackgroundMusicProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = volume;
    audio.loop = true;

    // Try to autoplay (might be blocked by browser)
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          setIsPlaying(true);
        })
        .catch((error) => {
          console.log('Autoplay was prevented:', error);
          setIsPlaying(false);
        });
    }

    return () => {
      audio.pause();
    };
  }, [volume]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play().then(() => {
        setIsPlaying(true);
      }).catch((error) => {
        console.error('Error playing audio:', error);
      });
    }
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.muted = !audio.muted;
    setIsMuted(!isMuted);
  };

  return (
    <div className="fixed bottom-4 left-4 z-50 flex gap-2">
      <audio ref={audioRef} src={src} preload="auto" />
      
      <Button
        variant="outline"
        size="sm"
        onClick={togglePlay}
        className="bg-black/50 backdrop-blur-sm border-primary/30 hover:bg-primary/20"
        title={isPlaying ? "Pausar música" : "Reproducir música"}
      >
        {isPlaying ? "⏸️" : "▶️"}
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={toggleMute}
        className="bg-black/50 backdrop-blur-sm border-primary/30 hover:bg-primary/20"
        title={isMuted ? "Activar sonido" : "Silenciar"}
      >
        {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
      </Button>
    </div>
  );
}
