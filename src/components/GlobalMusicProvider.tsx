"use client";
import { useEffect, useRef, useState } from 'react';
import { Volume2, VolumeX, Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function GlobalMusicProvider() {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = 0.15; // Volumen más bajo para que no moleste
    audio.loop = true;

    const handleCanPlayThrough = () => {
      setIsLoaded(true);
      setHasError(false);
    };

    const handleError = () => {
      setHasError(true);
      setIsLoaded(false);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    audio.addEventListener('canplaythrough', handleCanPlayThrough);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('error', handleError);

    // Intentar reproducir automáticamente (puede ser bloqueado por el navegador)
    const tryAutoplay = async () => {
      try {
        await audio.play();
      } catch (error) {
        console.log('Autoplay bloqueado por el navegador:', error);
      }
    };

    if (isLoaded) {
      tryAutoplay();
    }

    return () => {
      audio.removeEventListener('canplaythrough', handleCanPlayThrough);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('error', handleError);
    };
  }, [isLoaded]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
      } else {
        await audio.play();
      }
    } catch (error) {
      console.error('Error al controlar la reproducción:', error);
    }
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.muted = !audio.muted;
    setIsMuted(!isMuted);
  };

  return (
    <>
      <audio 
        ref={audioRef} 
        src="/hotline-miami.mp3" 
        preload="auto"
        onLoadedData={() => setIsLoaded(true)}
      />
      
      <div className="fixed bottom-4 left-4 z-50 flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={togglePlay}
          disabled={!isLoaded}
          className="bg-black/70 backdrop-blur-sm border-primary/30 hover:bg-primary/20 text-primary"
          title={isPlaying ? "Pausar música" : "Reproducir música"}
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={toggleMute}
          disabled={!isLoaded}
          className="bg-black/70 backdrop-blur-sm border-primary/30 hover:bg-primary/20 text-primary"
          title={isMuted ? "Activar sonido" : "Silenciar"}
        >
          {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
        </Button>

        {!isLoaded && !hasError && (
          <div className="bg-black/70 backdrop-blur-sm border border-primary/30 rounded px-2 py-1 text-xs text-primary">
            Cargando música...
          </div>
        )}

        {hasError && (
          <div className="bg-yellow-900/70 backdrop-blur-sm border border-yellow-500/30 rounded px-2 py-1 text-xs text-yellow-300">
            🎵 Música no disponible
          </div>
        )}
      </div>
    </>
  );
}
