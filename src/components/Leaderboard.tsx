"use client";
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Loader2 } from 'lucide-react';
import { redis } from '@/lib/upstash';

interface LeaderboardEntry {
  id: string;
  playerName: string;
  score: number;
}

export default function Leaderboard() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  const loadLeaderboard = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      if (!redis || typeof redis.zrange !== 'function') {
        console.warn('Redis not available for leaderboard');
        return;
      }

      const redisData = await redis.zrange('leaderboard', 0, 9, { withScores: true, rev: true });
      
      const entries: LeaderboardEntry[] = [];
      for (let i = 0; i < redisData.length; i += 2) {
        entries.push({
          id: redisData[i] as string,
          playerName: redisData[i] as string,
          score: Number(redisData[i + 1]),
        });
      }
      
      setLeaderboard(entries);
      setHasLoaded(true);
    } catch (error) {
      console.error('Error loading leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!hasLoaded) {
      loadLeaderboard();
    }
  }, [hasLoaded]);

  const handleRefresh = () => {
    setHasLoaded(false);
    setLeaderboard([]);
    loadLeaderboard();
  };

  return (
    <Card className="w-full max-w-md mx-auto neon-border-accent">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl text-accent flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Top 10 Global
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="text-xs"
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Actualizar"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading && !hasLoaded ? (
          <div className="flex justify-center items-center py-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="ml-2 text-accent">Cargando...</p>
          </div>
        ) : leaderboard.length > 0 ? (
          <ul className="space-y-2">
            {leaderboard.map((entry, index) => (
              <li
                key={entry.id}
                className="flex justify-between items-center p-3 rounded-md text-sm bg-card/50"
              >
                <span>#{index + 1}. {entry.playerName}</span>
                <span className="font-bold">{entry.score} pts</span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-muted-foreground text-center py-4 text-sm">
            No hay puntuaciones aún.
            <br />
            ¡Sé el primero en aparecer!
          </p>
        )}
      </CardContent>
    </Card>
  );
}
