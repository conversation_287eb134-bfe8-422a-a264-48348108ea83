
"use client";
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import type { BestPromptChallenge as ChallengeData } from '@/lib/challengesData';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckCircle, XCircle, Lightbulb, Target } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

interface Props {
  challenge: ChallengeData;
  onComplete: (answers: Record<string, string>, score: number) => void;
  isSubmitted: boolean;
}

export interface BestPromptChallengeViewHandle {
  triggerInternalSubmit: () => Promise<boolean>;
}

export const BestPromptChallengeView = forwardRef<BestPromptChallengeViewHandle, Props>(
  ({ challenge, onComplete, isSubmitted }, ref) => {
    const [answers, setAnswers] = useState<Record<string, string>>({}); 
    const [score, setScore] = useState(0);
    const { toast } = useToast();

    const calculateCurrentScore = (currentAnswers: Record<string,string>) => {
      let newScore = 0;
      challenge.scenarios.forEach(scenario => {
        if (currentAnswers[scenario.id] === scenario.correctOptionId) {
          newScore += challenge.pointsPerCorrect;
        }
      });
      return newScore;
    };

    useEffect(() => {
      if (isSubmitted) {
        const finalScore = calculateCurrentScore(answers);
        setScore(finalScore);
      }
    }, [isSubmitted, answers, challenge]);

    const handleAnswerChange = (scenarioId: string, optionId: string) => {
      if (isSubmitted) return;
      setAnswers(prev => ({ ...prev, [scenarioId]: optionId }));
    };
    
    const internalHandleSubmit = async (): Promise<boolean> => {
      if (Object.keys(answers).length !== challenge.scenarios.length) {
        toast({ title: "Espera", description: "Por favor, responde todos los escenarios.", variant: "destructive" });
        return false;
      }
      if (isSubmitted) return true;
      
      const calculatedScore = calculateCurrentScore(answers);
      setScore(calculatedScore);
      onComplete(answers, calculatedScore);
      toast({ 
        title: "¡Elecciones Enviadas!", 
        description: `Obtuviste ${calculatedScore} puntos. Revisa los detalles.`, 
        variant: "default" 
      });
      return true;
    };

    useImperativeHandle(ref, () => ({
      triggerInternalSubmit: internalHandleSubmit,
    }));

    return (
      <div className="space-y-6">
        <Accordion type="multiple" className="w-full space-y-4" defaultValue={challenge.scenarios.map((_, i) => `item-${i}`)}>
          {challenge.scenarios.map((scenario, index) => (
            <AccordionItem key={scenario.id} value={`item-${index}`} className="border-primary/50 rounded-lg overflow-hidden bg-card/70">
              <AccordionTrigger className={`p-4 text-xl hover:no-underline ${isSubmitted ? (answers[scenario.id] === scenario.correctOptionId ? 'bg-green-500/20 text-green-300' : 'bg-destructive/20 text-red-300') : 'hover:bg-primary/10 text-accent'}`}>
                <div className="flex items-center text-left">
                  <Target className="mr-3 h-6 w-6 flex-shrink-0"/> Escenario #{index + 1}: {scenario.taskDescription}
                  {isSubmitted && (
                    answers[scenario.id] === scenario.correctOptionId ?
                    <CheckCircle className="ml-auto h-6 w-6 text-green-400 flex-shrink-0" /> :
                    <XCircle className="ml-auto h-6 w-6 text-destructive flex-shrink-0" />
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4 space-y-3">
                <RadioGroup
                  value={answers[scenario.id]}
                  onValueChange={(value) => handleAnswerChange(scenario.id, value)}
                  disabled={isSubmitted}
                >
                  {scenario.options.map(option => (
                    <Label
                      key={option.id}
                      htmlFor={`${scenario.id}-${option.id}`}
                      className={`flex items-start space-x-3 p-3 rounded-md border-2 cursor-pointer transition-all
                        ${isSubmitted && option.id === scenario.correctOptionId ? 'border-green-500 bg-green-500/10 text-green-300' : ''}
                        ${isSubmitted && option.id !== scenario.correctOptionId && answers[scenario.id] === option.id ? 'border-destructive bg-destructive/10 text-red-300' : ''}
                        ${!isSubmitted && answers[scenario.id] === option.id ? 'border-primary bg-primary/20' : 'border-transparent hover:border-primary/50'}`}
                    >
                      <RadioGroupItem value={option.id} id={`${scenario.id}-${option.id}`} className="mt-1 border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground focus:ring-primary" />
                      <span className="text-base">{option.text}</span>
                    </Label>
                  ))}
                </RadioGroup>
                {isSubmitted && (
                  <div className="mt-3 p-3 bg-black/30 rounded-md">
                    <p className="text-sm text-yellow-300 flex items-start">
                      <Lightbulb className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                      <span><span className="font-bold">Opción Correcta ({scenario.correctOptionId}):</span> {scenario.options.find(o => o.id === scenario.correctOptionId)?.text}</span>
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">{scenario.explanation}</p>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {!isSubmitted && (
          <Button 
              onClick={internalHandleSubmit} 
              className="arcade-button w-full md:w-auto"
              disabled={Object.keys(answers).length !== challenge.scenarios.length}
          >
              Enviar Elecciones
          </Button>
        )}

        {isSubmitted && (
          <Card className="mt-6 bg-green-500/10 border-green-500">
            <CardHeader>
              <CardTitle className="text-2xl text-green-400">¡Desafío Completado!</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xl">Tu puntuación para este desafío: <span className="font-bold text-primary neon-glow-primary">{score} / {challenge.scenarios.length * challenge.pointsPerCorrect}</span></p>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }
);
BestPromptChallengeView.displayName = "BestPromptChallengeView";
