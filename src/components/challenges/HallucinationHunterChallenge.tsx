
"use client";
import React, { useState, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import type { HallucinationHunterChallenge as ChallengeData } from '@/lib/challengesData';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertTriangle, Lightbulb, Flag, Search } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from "@/hooks/use-toast";

interface Props {
  challenge: ChallengeData;
  onComplete: (selectedErrorIds: string[], score: number) => void;
  isSubmitted: boolean;
}

export interface HallucinationHunterChallengeViewHandle {
  triggerInternalSubmit: () => Promise<boolean>;
}

export const HallucinationHunterChallengeView = forwardRef<HallucinationHunterChallengeViewHandle, Props>(
  ({ challenge, onComplete, isSubmitted }, ref) => {
    const [selectedLineNumbers, setSelectedLineNumbers] = useState<number[]>([]);
    const [score, setScore] = useState(0);
    const { toast } = useToast();

    const codeLines = useMemo(() => {
      return challenge.codeWithErrors.split('\n').map((content, index) => ({
        number: index + 1,
        content: content,
        errorDetails: challenge.errorsToFind.find(e => e.line === index + 1) || null,
      }));
    }, [challenge.codeWithErrors, challenge.errorsToFind]);

    const calculateCurrentScore = (currentSelectedLines: number[]) => {
      let newScore = 0;
      const correctlySelectedErrorIds: string[] = [];

      currentSelectedLines.forEach(lineNumber => {
        const errorOnThisLine = challenge.errorsToFind.find(e => e.line === lineNumber);
        if (errorOnThisLine) {
          newScore += challenge.pointsPerError;
          correctlySelectedErrorIds.push(errorOnThisLine.id);
        }
        // No penalty for incorrect selections in this version, only points for correct ones.
      });
      return { correctlySelectedErrorIds, calculatedScore: newScore };
    };

    useEffect(() => {
      if (isSubmitted) {
        const { calculatedScore } = calculateCurrentScore(selectedLineNumbers);
        setScore(calculatedScore);
      }
    }, [isSubmitted, challenge.errorsToFind, challenge.pointsPerError, selectedLineNumbers]);


    const handleLineClick = (lineNumber: number) => {
      if (isSubmitted) return;
      setSelectedLineNumbers(prev =>
        prev.includes(lineNumber)
          ? prev.filter(ln => ln !== lineNumber)
          : [...prev, lineNumber]
      );
    };
    
    const internalHandleSubmit = async (): Promise<boolean> => {
      if (selectedLineNumbers.length === 0 && !isSubmitted) {
          toast({ title: "Espera", description: "Debes seleccionar al menos una línea donde creas que hay un error.", variant: "destructive" });
          return false;
      }
      if (isSubmitted) return true;

      const { correctlySelectedErrorIds, calculatedScore } = calculateCurrentScore(selectedLineNumbers);
      setScore(calculatedScore);
      onComplete(correctlySelectedErrorIds, calculatedScore);
      toast({ 
        title: "¡Análisis Enviado!", 
        description: `Obtuviste ${calculatedScore} puntos. Revisa los detalles.`, 
        variant: "default" 
      });
      return true;
    };

    useImperativeHandle(ref, () => ({
      triggerInternalSubmit: internalHandleSubmit,
    }));

    const getCorrectlyIdentifiedCount = () => {
      return challenge.errorsToFind.filter(error => selectedLineNumbers.includes(error.line)).length;
    };

    return (
      <div className="space-y-6">
        <Card className="bg-card/70 border-primary/50">
          <CardHeader>
            <CardTitle className="text-xl text-accent flex items-center">
              <Search className="mr-2 h-6 w-6"/> {challenge.title}
            </CardTitle>
            <CardDescription>
              {challenge.instructions}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-72 bg-black/50 rounded-md font-code text-sm neon-border">
              {codeLines.map((line) => {
                const isSelected = selectedLineNumbers.includes(line.number);
                const isActualError = !!line.errorDetails;
                
                let lineStyle = "flex hover:bg-primary/10 cursor-pointer transition-colors";
                let icon = null;

                if (isSubmitted) {
                  if (isActualError) {
                    if (isSelected) { 
                      lineStyle += " bg-green-500/30 border-l-4 border-green-500";
                      icon = <CheckCircle className="h-5 w-5 text-green-400" />;
                    } else { 
                      lineStyle += " bg-destructive/30 border-l-4 border-destructive";
                      icon = <XCircle className="h-5 w-5 text-destructive" />;
                    }
                  } else if (isSelected) { 
                    lineStyle += " bg-yellow-500/30 border-l-4 border-yellow-500";
                    icon = <AlertTriangle className="h-5 w-5 text-yellow-400" />;
                  }
                } else if (isSelected) {
                  lineStyle += " bg-primary/30 border-l-4 border-primary";
                  icon = <Flag className="h-5 w-5 text-primary" />;
                }

                return (
                  <div
                    key={line.number}
                    className={lineStyle}
                    onClick={() => handleLineClick(line.number)}
                  >
                    <div className={`w-12 flex-shrink-0 text-muted-foreground text-right pr-3 py-1.5 bg-background/60 border-r border-primary/30 flex items-center justify-end ${isSubmitted && isActualError && !isSelected ? 'font-bold text-destructive' : ''}`}>
                      {line.number}
                    </div>
                    <div className="flex-1 px-4 py-1.5">
                      <pre className="whitespace-pre-wrap"><code>{line.content || ' '}</code></pre>
                    </div>
                    {icon && (
                      <div className="w-10 flex-shrink-0 flex items-center justify-center px-2 py-1.5">
                        {icon}
                      </div>
                    )}
                  </div>
                );
              })}
            </ScrollArea>
          </CardContent>
        </Card>
        
        {!isSubmitted && (
           <Button 
              onClick={internalHandleSubmit} 
              className="arcade-button w-full md:w-auto"
              disabled={selectedLineNumbers.length === 0}
          >
              Enviar Análisis
          </Button>
        )}

        {isSubmitted && (
          <>
            <Card className="mt-6 bg-primary/10 border-primary">
              <CardHeader>
                <CardTitle className="text-2xl neon-text-readable">¡Análisis Completado!</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl">Tu puntuación: <span className="font-bold neon-text-accent-readable">{score} / {challenge.errorsToFind.length * challenge.pointsPerError}</span></p>
                <p className="text-muted-foreground">Identificaste correctamente {getCorrectlyIdentifiedCount()} de {challenge.errorsToFind.length} errores reales.</p>
              </CardContent>
            </Card>

            <Card className="mt-6 bg-card/70 border-accent/50">
              <CardHeader>
                <CardTitle className="text-xl text-accent flex items-center"><Lightbulb className="mr-2"/>Detalle de Errores</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {challenge.errorsToFind.map(error => {
                  const userFoundThisError = selectedLineNumbers.includes(error.line);
                  return (
                    <div key={error.id} className={`p-3 rounded-md border-l-4 ${userFoundThisError ? 'border-green-500 bg-green-500/10' : 'border-destructive bg-destructive/10'}`}>
                      <div className="flex justify-between items-center mb-1">
                        <p className="font-semibold text-primary">Error en línea {error.line}: <span className="text-foreground">{error.description}</span></p>
                        {userFoundThisError ? 
                          <Badge variant="default" className="bg-green-500 hover:bg-green-600">Encontrado</Badge> :
                          <Badge variant="destructive">Omitido</Badge>
                        }
                      </div>
                      <p className="text-sm text-muted-foreground italic">{error.explanation}</p>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    );
  }
);
HallucinationHunterChallengeView.displayName = "HallucinationHunterChallengeView";
