
"use client";
import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import type { HumanOrAiChallenge as ChallengeData } from '@/lib/challengesData';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Brain, Bot } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from "@/hooks/use-toast";

interface Props {
  challenge: ChallengeData;
  onComplete: (answers: Record<string, string>, score: number) => void;
  isSubmitted: boolean;
}

export interface HumanOrAiChallengeViewHandle {
  triggerInternalSubmit: () => Promise<boolean>;
}

export const HumanOrAiChallengeView = forwardRef<HumanOrAiChallengeViewHandle, Props>(
  ({ challenge, onComplete, isSubmitted }, ref) => {
    const [answers, setAnswers] = useState<Record<string, string>>({}); 
    const [score, setScore] = useState(0);
    const { toast } = useToast();

    const calculateCurrentScore = (currentAnswers: Record<string,string>) => {
      let newScore = 0;
      challenge.snippets.forEach(snippet => {
        const userAnswer = currentAnswers[snippet.id];
        const correctAnswer = snippet.isHuman ? 'human' : 'ai';
        if (userAnswer === correctAnswer) {
          newScore += challenge.pointsPerCorrect;
        }
      });
      return newScore;
    }

    useEffect(() => {
      if (isSubmitted) {
        const finalScore = calculateCurrentScore(answers);
        setScore(finalScore);
      }
    }, [isSubmitted, answers, challenge]);


    const handleAnswerChange = (snippetId: string, value: string) => {
      if (isSubmitted) return;
      setAnswers(prev => ({ ...prev, [snippetId]: value }));
    };
    
    const internalHandleSubmit = async (): Promise<boolean> => {
      if (Object.keys(answers).length !== challenge.snippets.length) {
        toast({ title: "Espera", description: "Por favor, responde para todos los fragmentos.", variant: "destructive" });
        return false;
      }
      if (isSubmitted) return true;

      const calculatedScore = calculateCurrentScore(answers);
      setScore(calculatedScore); 
      onComplete(answers, calculatedScore);
      toast({ 
        title: "¡Respuestas Enviadas!", 
        description: `Obtuviste ${calculatedScore} puntos. Revisa los detalles.`, 
        variant: "default" 
      });
      return true;
    };

    useImperativeHandle(ref, () => ({
      triggerInternalSubmit: internalHandleSubmit,
    }));

    return (
      <div className="space-y-6">
        {challenge.snippets.map((snippet, index) => (
          <Card key={snippet.id} className={`bg-card/70 ${isSubmitted ? (answers[snippet.id] === (snippet.isHuman ? 'human' : 'ai') ? 'border-green-500' : 'border-destructive') : 'border-primary/50'}`}>
            <CardHeader>
              <CardTitle className="text-xl text-accent">Fragmento #{index + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-48 bg-black/50 p-3 rounded-md font-code text-sm neon-border">
                <pre><code>{snippet.code}</code></pre>
              </ScrollArea>
              {!isSubmitted && (
                <RadioGroup
                  value={answers[snippet.id]}
                  onValueChange={(value) => handleAnswerChange(snippet.id, value)}
                  className="mt-4 flex gap-4"
                  disabled={isSubmitted}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="human" id={`${snippet.id}-human`} className="text-primary focus:ring-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"/>
                    <Label htmlFor={`${snippet.id}-human`} className="text-lg flex items-center"><Brain className="mr-2 h-5 w-5 text-green-400"/> Humano</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ai" id={`${snippet.id}-ai`} className="text-primary focus:ring-primary data-[state=checked]:border-primary data-[state=checked]:bg-primary"/>
                    <Label htmlFor={`${snippet.id}-ai`} className="text-lg flex items-center"><Bot className="mr-2 h-5 w-5 text-blue-400"/> IA</Label>
                  </div>
                </RadioGroup>
              )}
              {isSubmitted && (
                <div className="mt-4 p-3 rounded-md">
                  <p className="text-lg">Tu respuesta: <span className={`font-bold ${answers[snippet.id] === (snippet.isHuman ? 'human' : 'ai') ? 'text-green-400' : 'text-destructive'}`}>{answers[snippet.id]?.toUpperCase() || "Sin Responder"}</span></p>
                  <p className="text-lg">Respuesta correcta: <span className="font-bold text-primary">{snippet.isHuman ? 'HUMANO' : 'IA'}</span></p>
                  <p className="text-sm text-muted-foreground mt-1">{snippet.explanation}</p>
                  {answers[snippet.id] === (snippet.isHuman ? 'human' : 'ai') ? 
                    <CheckCircle className="h-6 w-6 text-green-400 inline-block ml-2" /> : 
                    <XCircle className="h-6 w-6 text-destructive inline-block ml-2" />}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
        {!isSubmitted && (
           <Button 
              onClick={internalHandleSubmit} 
              className="arcade-button w-full md:w-auto"
              disabled={Object.keys(answers).length !== challenge.snippets.length}
          >
              Enviar Respuestas
          </Button>
        )}
        {isSubmitted && (
          <Card className="mt-6 bg-green-500/10 border-green-500">
            <CardHeader>
              <CardTitle className="text-2xl text-green-400">¡Desafío Completado!</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xl">Tu puntuación para este desafío: <span className="font-bold text-primary neon-glow-primary">{score} / {challenge.snippets.length * challenge.pointsPerCorrect}</span></p>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }
);
HumanOrAiChallengeView.displayName = "HumanOrAiChallengeView";
