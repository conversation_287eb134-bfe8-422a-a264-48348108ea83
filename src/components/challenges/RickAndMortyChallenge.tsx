
"use client";
import React, { useState, useImperativeHandle, forwardRef } from 'react';
import type { PromptChallenge as ChallengeData } from '@/lib/challengesData';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { scoreRickMortyPrompt, type ScoreRickMortyPromptOutput } from '@/ai/flows/score-rick-morty-prompt';
import { Loader2, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

interface Props {
  challenge: ChallengeData;
  onSubmit: (answer: string, score: number, feedback: { rickMorty: ScoreRickMortyPromptOutput }) => void;
  isSubmitted: boolean;
}

export interface RickAndMortyChallengeHandle {
  triggerInternalSubmit: () => Promise<boolean>;
}

export const RickAndMortyChallenge = forwardRef<RickAndMortyChallengeHandle, Props>(
  ({ challenge, onSubmit, isSubmitted }, ref) => {
    const [promptText, setPromptText] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [evaluationResult, setEvaluationResult] = useState<ScoreRickMortyPromptOutput | null>(null);
    const { toast } = useToast();

    const internalHandleSubmit = async (): Promise<boolean> => {
      if (!promptText.trim()) {
        toast({ title: "Error", description: "El prompt no puede estar vacío.", variant: "destructive" });
        return false;
      }
      if (isSubmitted) return true;

      setIsLoading(true);
      setEvaluationResult(null);
      try {
        const result = await scoreRickMortyPrompt({ prompt: promptText });
        setEvaluationResult(result);
        onSubmit(promptText, result.overallScore, { rickMorty: result });
        toast({ 
          title: "¡Evaluación Completa!", 
          description: `Tu prompt obtuvo ${result.overallScore}/100. Revisa el feedback en español a continuación.`, 
          variant: "default" 
        });
        setIsLoading(false);
        return true;
      } catch (error) {
        console.error("Error evaluando prompt:", error);
        toast({ 
          title: "Error de Evaluación", 
          description: "No se pudo evaluar el prompt. Por favor, inténtalo de nuevo.", 
          variant: "destructive" 
        });
        setIsLoading(false);
        return false;
      }
    };

    useImperativeHandle(ref, () => ({
      triggerInternalSubmit: internalHandleSubmit,
    }));

    return (
      <div className="space-y-6">
        {challenge.apiDocLink && (
          <p className="text-sm text-accent">
            Documentación de la API: <a href={challenge.apiDocLink} target="_blank" rel="noopener noreferrer" className="underline hover:text-primary">{challenge.apiDocLink}</a>
          </p>
        )}
        <Textarea
          value={promptText}
          onChange={(e) => setPromptText(e.target.value)}
          placeholder="Escribe aquí tu prompt para la API de Rick y Morty..."
          rows={10}
          className="arcade-input text-base"
          disabled={isSubmitted || isLoading}
        />
        {!isSubmitted && (
          <Button onClick={internalHandleSubmit} disabled={isLoading || !promptText.trim()} className="arcade-button w-full md:w-auto">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
            Evaluar Prompt
          </Button>
        )}

        {evaluationResult && (
          <Card className="mt-6 bg-card/80 border-accent/70 neon-border">
            <CardHeader>
              <CardTitle className="text-2xl text-accent flex items-center">
                {evaluationResult.overallScore >= 70 ? <CheckCircle className="mr-2 h-6 w-6 text-green-400" /> : <AlertTriangle className="mr-2 h-6 w-6 text-yellow-400" />}
                Puntuación General: <span className="ml-2 text-primary neon-glow-primary">{evaluationResult.overallScore} / 100</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-primary flex items-center mb-1">
                  <Info className="mr-2 h-5 w-5"/>Feedback Detallado (en Español):
                </h3>
                <p className="whitespace-pre-wrap p-3 bg-black/30 rounded-md text-sm leading-relaxed">{evaluationResult.feedback}</p>
              </div>
              {(evaluationResult.clarity !== undefined || evaluationResult.comprehensibility !== undefined || evaluationResult.organization !== undefined || evaluationResult.detail !== undefined) && (
                   <details className="bg-black/20 p-3 rounded-md">
                      <summary className="text-sm text-muted-foreground cursor-pointer hover:text-accent">Ver puntuaciones por categoría (si disponibles)</summary>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 mt-2 text-xs">
                          {evaluationResult.clarity !== undefined && <div><strong className="text-primary/80">Claridad:</strong> {evaluationResult.clarity}/30</div>}
                          {evaluationResult.comprehensibility !== undefined && <div><strong className="text-primary/80">Comprensibilidad:</strong> {evaluationResult.comprehensibility}/25</div>}
                          {evaluationResult.organization !== undefined && <div><strong className="text-primary/80">Organización:</strong> {evaluationResult.organization}/25</div>}
                          {evaluationResult.detail !== undefined && <div><strong className="text-primary/80">Detalle:</strong> {evaluationResult.detail}/20</div>}
                      </div>
                   </details>
              )}
            </CardContent>
          </Card>
        )}
        {isSubmitted && !evaluationResult && !isLoading && (
          <p className="text-center text-yellow-400">Este desafío ya fue procesado. Espera al siguiente o revisa los resultados si ya fueron cargados.</p>
        )}
      </div>
    );
  }
);

RickAndMortyChallenge.displayName = "RickAndMortyChallenge";
