
"use client";
import React, { useState, useImperativeHandle, forwardRef } from 'react';
import type { PromptEvaluationChallenge as ChallengeData } from '@/lib/challengesData';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { evaluatePromptTechnical, type EvaluatePromptTechnicalOutput } from '@/ai/flows/evaluate-prompt-technical';
import { Loader2, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

interface Props {
  challenge: ChallengeData;
  onSubmit: (answer: string, score: number, feedback: { technical: EvaluatePromptTechnicalOutput }) => void;
  isSubmitted: boolean;
}

export interface TechnicalPromptChallengeHandle {
  triggerInternalSubmit: () => Promise<boolean>;
}

export const TechnicalPromptChallenge = forwardRef<TechnicalPromptChallengeHandle, Props>(
  ({ challenge, onSubmit, isSubmitted }, ref) => {
    const [promptText, setPromptText] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [evaluationResult, setEvaluationResult] = useState<EvaluatePromptTechnicalOutput | null>(null);
    const { toast } = useToast();

    const internalHandleSubmit = async (): Promise<boolean> => {
      if (!promptText.trim()) {
        toast({ title: "Error", description: "El prompt no puede estar vacío.", variant: "destructive" });
        return false;
      }
      if (isSubmitted) return true; 

      setIsLoading(true);
      setEvaluationResult(null);
      try {
        const result = await evaluatePromptTechnical({ prompt: promptText });
        setEvaluationResult(result);
        onSubmit(promptText, result.score, { technical: result });
        toast({ 
          title: "¡Evaluación Completa!", 
          description: `Tu prompt obtuvo ${result.score}/100. Revisa el feedback en español a continuación.`, 
          variant: "default" 
        });
        setIsLoading(false);
        return true;
      } catch (error) {
        console.error("Error evaluando prompt:", error);
        toast({ 
          title: "Error de Evaluación", 
          description: "No se pudo evaluar el prompt. Por favor, inténtalo de nuevo o contacta soporte.", 
          variant: "destructive" 
        });
        setIsLoading(false);
        return false;
      }
    };

    useImperativeHandle(ref, () => ({
      triggerInternalSubmit: internalHandleSubmit,
    }));

    return (
      <div className="space-y-6">
        <Textarea
          value={promptText}
          onChange={(e) => setPromptText(e.target.value)}
          placeholder="Escribe aquí tu prompt para C# Dólar Blue API..."
          rows={10}
          className="arcade-input text-base"
          disabled={isSubmitted || isLoading}
        />
        {!isSubmitted && (
          <Button onClick={internalHandleSubmit} disabled={isLoading || !promptText.trim()} className="arcade-button w-full md:w-auto">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
            Evaluar Prompt
          </Button>
        )}

        {evaluationResult && (
          <Card className="mt-6 bg-card/80 border-accent/70 neon-border">
            <CardHeader>
              <CardTitle className="text-2xl text-accent flex items-center">
                {evaluationResult.score >= 70 ? <CheckCircle className="mr-2 h-6 w-6 text-green-400" /> : <AlertTriangle className="mr-2 h-6 w-6 text-yellow-400" />}
                Puntuación de Evaluación: <span className="ml-2 text-primary neon-glow-primary">{evaluationResult.score} / 100</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
               <h3 className="text-lg font-semibold text-primary flex items-center mb-1">
                  <Info className="mr-2 h-5 w-5"/>Feedback Detallado (en Español):
                </h3>
              <p className="whitespace-pre-wrap mt-2 p-3 bg-black/30 rounded-md text-sm leading-relaxed">{evaluationResult.evaluationDetails}</p>
            </CardContent>
          </Card>
        )}
         {isSubmitted && !evaluationResult && !isLoading && (
          <p className="text-center text-yellow-400">Este desafío ya fue procesado. Espera al siguiente o revisa los resultados si ya fueron cargados.</p>
        )}
      </div>
    );
  }
);

TechnicalPromptChallenge.displayName = "TechnicalPromptChallenge";
