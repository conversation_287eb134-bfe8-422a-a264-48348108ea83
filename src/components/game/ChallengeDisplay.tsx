
"use client";
import type { ReactNode } from 'react';
import type { Challenge } from '@/lib/challengesData';
import { ChallengeTimer } from './ChallengeTimer';
import { GameProgressBar } from './ProgressBar';
import { useGame } from '@/context/GameContext';
import { But<PERSON> } from '@/components/ui/button';
import { Info } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface ChallengeDisplayProps {
  challenge: Challenge | null;
  children: ReactNode;
  onTimeUp: () => void;
  onNext: () => void;
}

export function ChallengeDisplay({
  challenge,
  children,
  onTimeUp,
  onNext,
}: ChallengeDisplayProps) {
  const { currentChallengeIndex, totalChallenges } = useGame();

  if (!challenge) {
    return <div className="text-center text-2xl text-destructive">Error: Desafío no encontrado.</div>;
  }

  const isLastChallenge = currentChallengeIndex === totalChallenges - 1;
  const nextButtonText = isLastChallenge ? 'Ver Resultados Finales' : 'Siguiente Desafío';

  return (
    <div className="space-y-8 animate-slide-in">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 p-4 bg-card/80 rounded-lg neon-border-accent">
        <h1 className="text-3xl md:text-4xl font-headline text-primary neon-glow-primary text-center md:text-left">
          {challenge.title}
        </h1>
        <ChallengeTimer initialMinutes={challenge.timeLimit} onTimeUp={onTimeUp} />
      </div>

      <GameProgressBar current={currentChallengeIndex} total={totalChallenges} />

      <div className="bg-card p-6 rounded-lg shadow-xl neon-border space-y-4">
        <div className="flex justify-between items-start">
          <h2 className="text-2xl font-semibold text-accent">Detalles del Desafío:</h2>
          {challenge.instructions && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" className="border-accent text-accent hover:bg-accent hover:text-background">
                  <Info className="mr-2 h-5 w-5" /> Instrucciones
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-card text-foreground neon-border">
                <AlertDialogHeader>
                  <AlertDialogTitle className="text-primary neon-glow-primary text-2xl">Instrucciones del Desafío</AlertDialogTitle>
                  <AlertDialogDescription className="text-lg whitespace-pre-wrap">
                    {challenge.instructions}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogAction className="arcade-button">¡Entendido!</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
        <p className="text-lg whitespace-pre-wrap leading-relaxed">{challenge.description}</p>
        
        <div className="mt-6">{children}</div>
      </div>

      <div className="flex justify-end mt-8">
        <Button 
          onClick={onNext} 
          className="arcade-button-accent text-xl"
          // The button is generally always enabled; its action (onNext) handles logic.
          // A global "isProcessingNextStep" state could disable it if needed.
        >
          {nextButtonText}
        </Button>
      </div>
    </div>
  );
}
