
"use client";
import { useState, useEffect } from 'react';
import { TimerIcon } from 'lucide-react';

interface ChallengeTimerProps {
  initialMinutes: number;
  onTimeUp: () => void;
  isPaused?: boolean;
}

export function ChallengeTimer({ initialMinutes, onTimeUp, isPaused = false }: ChallengeTimerProps) {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60);

  useEffect(() => {
    setTimeLeft(initialMinutes * 60);
  }, [initialMinutes]);

  useEffect(() => {
    if (timeLeft <= 0) {
      onTimeUp();
      return;
    }

    if (isPaused) return;

    const intervalId = setInterval(() => {
      setTimeLeft((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, [timeLeft, onTimeUp, isPaused]);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  const timeColorClass = timeLeft <= 60 ? 'text-destructive neon-glow-primary animate-pulse' : timeLeft <= 180 ? 'text-yellow-400' : 'text-accent';

  return (
    <div className={`flex items-center space-x-2 p-2 rounded-md bg-card/50 border-2 border-primary shadow-lg ${timeColorClass}`}>
      <TimerIcon className="h-8 w-8" />
      <span className="text-3xl font-headline tabular-nums">
        {String(minutes).padStart(2, '0')}:{String(seconds).padStart(2, '0')}
      </span>
    </div>
  );
}
