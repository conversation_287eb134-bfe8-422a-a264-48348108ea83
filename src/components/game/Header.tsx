'use client';
import Link from 'next/link';
import { useGame } from '@/context/GameContext';
import { Button } from '@/components/ui/button';
import { Home, RefreshCw } from 'lucide-react';

export function Header() {
  const { playerName, currentScore, resetGame, gameOver, gameStarted } = useGame();

  return (
    <header className="bg-gradient-to-r from-purple-900/20 via-black to-cyan-900/20 p-4 shadow-2xl neon-border-accent border-b-2 relative scan-lines">
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/10 to-transparent animate-pulse"></div>
      <div className="container mx-auto flex justify-between items-center relative z-10">
        <Link href="/" className="text-3xl font-headline neon-glow-primary animate-neon-pulse hover:scale-105 transition-transform">
          PROMPT BATTLE
        </Link>
        <div className="flex items-center space-x-4">
          {gameStarted && !gameOver && playerName && (
            <div className="text-lg bg-black/30 px-4 py-2 rounded border border-accent/50 backdrop-blur-sm">
              <span className="font-headline text-accent neon-glow-accent">{playerName}</span> | 
              <span className="text-primary font-bold neon-glow-primary ml-2">SCORE: {currentScore}</span>
            </div>
          )}
          {(gameOver || !gameStarted) && (
             <Button variant="ghost" size="icon" onClick={resetGame} title="Reiniciar Juego / Ir al Inicio" className="hover:bg-primary/20 border border-primary/30">
               <Home className="h-6 w-6 text-accent hover:text-primary transition-colors" />
             </Button>
          )}
          {gameStarted && !gameOver && (
             <Button variant="ghost" size="icon" onClick={resetGame} title="Reiniciar Juego" className="hover:bg-accent/20 border border-accent/30">
               <RefreshCw className="h-6 w-6 text-accent hover:text-primary transition-colors" />
             </Button>
           )}
        </div>
      </div>
    </header>
  );
}
