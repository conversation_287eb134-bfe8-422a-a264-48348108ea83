'use client';
import Link from 'next/link';
import { useGame } from '@/context/GameContext';
import { Button } from '@/components/ui/button';
import { Home, RefreshCw } from 'lucide-react';

export function Header() {
  const { playerName, currentScore, resetGame, gameOver, gameStarted } = useGame();

  return (
    <header className="bg-card p-4 shadow-md neon-border-accent border-b-2">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-3xl font-headline neon-glow-primary">
          Prompt Battle
        </Link>
        <div className="flex items-center space-x-4">
          {gameStarted && !gameOver && playerName && (
            <div className="text-lg">
              <span className="font-headline text-accent">{playerName}</span> | Puntuación: <span className="font-bold text-primary neon-glow-primary">{currentScore}</span>
            </div>
          )}
          {(gameOver || !gameStarted) && (
             <Button variant="ghost" size="icon" onClick={resetGame} title="Reiniciar Juego / Ir al Inicio">
               <Home className="h-6 w-6 text-accent hover:text-primary" />
             </Button>
          )}
           {gameStarted && !gameOver && (
             <Button variant="ghost" size="icon" onClick={resetGame} title="Reiniciar Juego">
               <RefreshCw className="h-6 w-6 text-accent hover:text-primary" />
             </Button>
           )}
        </div>
      </div>
    </header>
  );
}
