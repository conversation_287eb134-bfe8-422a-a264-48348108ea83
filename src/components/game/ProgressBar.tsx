
"use client";
import { Progress } from "@/components/ui/progress";

interface ProgressBarProps {
  current: number;
  total: number;
}

export function GameProgressBar({ current, total }: ProgressBarProps) {
  const progressPercentage = total > 0 ? ((current +1) / total) * 100 : 0;

  return (
    <div className="w-full space-y-2">
      <div className="flex justify-between text-sm font-medium text-accent">
        <span>Desafío {Math.min(current + 1, total)} de {total}</span>
        <span>{Math.round(progressPercentage)}%</span>
      </div>
      <Progress value={progressPercentage} className="h-4 [&>div]:bg-gradient-to-r [&>div]:from-primary [&>div]:to-accent" />
    </div>
  );
}
