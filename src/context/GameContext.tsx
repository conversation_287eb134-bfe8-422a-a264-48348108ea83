
"use client";
import type { ReactNode } from 'react';
import React, { createContext, useState, useContext, useCallback } from 'react';
import { challenges, type Challenge } from '@/lib/challengesData';
import { useRouter } from 'next/navigation';
import type { EvaluatePromptTechnicalOutput } from '@/ai/flows/evaluate-prompt-technical';
import type { ScoreRickMortyPromptOutput } from '@/ai/flows/score-rick-morty-prompt';
import { redis } from '@/lib/upstash'; 

export interface Player {
  id: string;
  name: string;
  score: number;
}

export interface Answer {
  challengeId: number;
  value: any;
  score: number;
  technicalPromptFeedback?: EvaluatePromptTechnicalOutput;
  rickAndMortyFeedback?: ScoreRickMortyPromptOutput;
}

interface GameState {
  playerName: string | null;
  players: Player[]; 
  currentChallengeIndex: number;
  totalChallenges: number;
  currentScore: number; 
  answers: Answer[];
  gameStarted: boolean;
  gameOver: boolean;
  setPlayerName: (name: string) => void;
  startGame: () => void;
  submitAnswer: (
    challengeId: number, 
    answerValue: any, 
    score: number, 
    feedback?: { technical?: EvaluatePromptTechnicalOutput, rickAndMorty?: ScoreRickMortyPromptOutput }
  ) => void;
  nextChallenge: () => void;
  endGame: () => void; 
  resetGame: () => void;
  getCurrentChallenge: () => Challenge | null;
}

const GameContext = createContext<GameState | undefined>(undefined);

export const GameProvider = ({ children }: { children: ReactNode }) => {
  const router = useRouter();
  const [playerName, setPlayerNameState] = useState<string | null>(null);
  const [players, setPlayersState] = useState<Player[]>([]); 
  const [currentChallengeIndex, setCurrentChallengeIndex] = useState<number>(0);
  const [currentScore, setCurrentScore] = useState<number>(0);
  const [answers, setAnswers] = useState<Answer[]>([]);
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [gameOver, setGameOver] = useState<boolean>(false);
  const totalChallenges = challenges.length;

  const setPlayerName = useCallback((name: string) => {
    setPlayerNameState(name);
  }, []);

  const startGame = useCallback(() => {
    if (!playerName) {
      router.push('/register');
      return;
    }
    const realPlayer: Player = { id: Date.now().toString(), name: playerName, score: 0 };
    
    setPlayersState([realPlayer]);
    setGameStarted(true);
    setGameOver(false);
    setCurrentChallengeIndex(0);
    setCurrentScore(0); 
    setAnswers([]);
    if (challenges.length > 0) {
      router.push(`/challenge/${challenges[0].id}`);
    } else {
      console.error("No challenges loaded, cannot start game.");
      setGameOver(true);
      router.push('/results');
    }
  }, [playerName, router, challenges]);

  const submitAnswer = useCallback((
    challengeId: number, 
    answerValue: any, 
    score: number,
    feedback?: { technical?: EvaluatePromptTechnicalOutput, rickAndMorty?: ScoreRickMortyPromptOutput }
  ) => {
    if (answers.some(ans => ans.challengeId === challengeId)) {
      console.warn(`GameContext: Se intentó enviar una respuesta duplicada para el desafío ${challengeId}. Solo se cuenta la primera.`);
      return;
    }

    const newAnswer: Answer = { 
      challengeId, 
      value: answerValue, 
      score,
      technicalPromptFeedback: feedback?.technical,
      rickAndMortyFeedback: feedback?.rickMorty,
    };
    setAnswers(prevAnswers => [...prevAnswers, newAnswer]);
    
    setCurrentScore(prevScore => prevScore + score);
    setPlayersState(prevPlayers => 
      prevPlayers.map(p => 
        p.name === playerName ? { ...p, score: p.score + score } : p
      )
    );
  }, [playerName, answers]);

  const saveScoreToLeaderboard = useCallback(async () => {
    if (!redis || !playerName || currentScore === undefined) {
      console.warn("GameContext: Cannot save score. Upstash Redis not init, or no player name/score.", { redisExists: !!redis, playerName, currentScore });
      return;
    }
    console.log("GameContext: Attempting to save score to Upstash leaderboard:", { playerName, currentScore });
    try {
      // Use ZADD to add/update the player's score in the sorted set.
      // If player exists, score is updated. If not, player is added.
      // We store the score as the value and playerName as the member.
      await redis.zadd('leaderboard', { score: currentScore, member: playerName });
      console.log('GameContext: Puntuación guardada en el marcador global de Upstash.');
    } catch (error) {
      console.error('GameContext: Error al guardar la puntuación en el marcador global de Upstash:', error);
    }
  }, [playerName, currentScore]);

  const nextChallenge = useCallback(async () => {
    console.log("GameContext: nextChallenge() called. Current index:", currentChallengeIndex, "Total challenges:", totalChallenges);
    if (currentChallengeIndex < totalChallenges - 1) {
      const nextIndex = currentChallengeIndex + 1;
      setCurrentChallengeIndex(nextIndex);
      if (challenges[nextIndex]) {
        router.push(`/challenge/${challenges[nextIndex].id}`);
      } else {
        console.error("GameContext: nextChallenge - Next challenge data not found, ending game.");
        setGameOver(true); 
        router.push('/results');
      }
    } else {
      console.log("GameContext: nextChallenge - Reached end of challenges.");
      try {
        await saveScoreToLeaderboard(); 
        console.log("GameContext: nextChallenge - Score saved (or attempt finished).");
      } catch (error) {
        console.error("GameContext: nextChallenge - Error during saveScoreToLeaderboard, but proceeding to set gameOver:", error);
      }
      console.log("GameContext: nextChallenge - Setting gameOver=true.");
      setGameOver(true);
    }
  }, [currentChallengeIndex, totalChallenges, router, challenges, saveScoreToLeaderboard]); 

  const endGame = useCallback(async () => {
    console.log("GameContext: endGame called.");
    try {
      await saveScoreToLeaderboard();
      console.log("GameContext: endGame - Score saved (or attempt finished).");
    } catch (error) {
      console.error("GameContext: endGame - Error during saveScoreToLeaderboard, but proceeding to set gameOver:", error);
    }
    setGameOver(true);
    router.push('/results'); 
  }, [router, saveScoreToLeaderboard]); 

  const resetGame = useCallback(() => {
    setPlayerNameState(null);
    setPlayersState([]);
    setCurrentChallengeIndex(0);
    setCurrentScore(0);
    setAnswers([]);
    setGameStarted(false);
    setGameOver(false);
    router.push('/');
  }, [router]);

  const getCurrentChallenge = useCallback(() => {
    if (gameStarted && !gameOver && currentChallengeIndex >= 0 && currentChallengeIndex < challenges.length) {
      return challenges[currentChallengeIndex];
    }
    return null;
  }, [currentChallengeIndex, gameStarted, gameOver, challenges]);

  return (
    <GameContext.Provider value={{
      playerName,
      players,
      currentChallengeIndex,
      totalChallenges,
      currentScore,
      answers,
      gameStarted,
      gameOver,
      setPlayerName,
      startGame,
      submitAnswer,
      nextChallenge,
      endGame,
      resetGame,
      getCurrentChallenge,
    }}>
      {children}
    </GameContext.Provider>
  );
};

export const useGame = (): GameState => {
  const context = useContext(GameContext);
  if (context === undefined) {
    throw new Error('useGame debe ser utilizado dentro de un GameProvider');
  }
  return context;
};
