import { redis } from './upstash';
import type { Answer } from '@/context/GameContext';

export interface ChallengeResponse {
  id: string;
  playerName: string;
  challengeId: number;
  response: any;
  score: number;
  timestamp: number;
  feedback?: {
    technical?: any;
    rickMorty?: any;
  };
}

export interface PlayerStats {
  playerName: string;
  totalScore: number;
  challengesCompleted: number;
  averageScore: number;
  bestChallenge: {
    challengeId: number;
    score: number;
  };
  responses: ChallengeResponse[];
}

/**
 * Save a challenge response to Redis
 */
export async function saveChallengeResponse(
  playerName: string,
  challengeId: number,
  response: any,
  score: number,
  feedback?: { technical?: any; rickMorty?: any }
): Promise<boolean> {
  if (!redis) {
    console.warn('Redis not available, cannot save challenge response');
    return false;
  }

  try {
    const responseId = `${playerName}-${challengeId}-${Date.now()}`;
    const challengeResponse: ChallengeResponse = {
      id: responseId,
      playerName,
      challengeId,
      response,
      score,
      timestamp: Date.now(),
      feedback
    };

    // Save individual response
    await redis.set(`response:${responseId}`, JSON.stringify(challengeResponse), { ex: 86400 * 30 }); // 30 days

    // Add to player's response list
    await redis.zadd(`player:${playerName}:responses`, { score: Date.now(), member: responseId });

    // Add to challenge response list
    await redis.zadd(`challenge:${challengeId}:responses`, { score: Date.now(), member: responseId });

    // Update player stats
    await updatePlayerStats(playerName, challengeId, score);

    console.log(`Challenge response saved: ${responseId}`);
    return true;
  } catch (error) {
    console.error('Error saving challenge response:', error);
    return false;
  }
}

/**
 * Get all responses for a player
 */
export async function getPlayerResponses(playerName: string): Promise<ChallengeResponse[]> {
  if (!redis) {
    console.warn('Redis not available, cannot get player responses');
    return [];
  }

  try {
    // Get response IDs for the player
    const responseIds = await redis.zrange(`player:${playerName}:responses`, 0, -1, { rev: true });
    
    if (responseIds.length === 0) {
      return [];
    }

    // Get all responses
    const responses: ChallengeResponse[] = [];
    for (const responseId of responseIds) {
      const responseData = await redis.get(`response:${responseId}`);
      if (responseData) {
        // Upstash Redis client returns objects directly, no need to parse
        const response = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
        responses.push(response as ChallengeResponse);
      }
    }

    return responses;
  } catch (error) {
    console.error('Error getting player responses:', error);
    return [];
  }
}

/**
 * Get all responses for a specific challenge
 */
export async function getChallengeResponses(challengeId: number): Promise<ChallengeResponse[]> {
  if (!redis) {
    console.warn('Redis not available, cannot get challenge responses');
    return [];
  }

  try {
    // Get response IDs for the challenge
    const responseIds = await redis.zrange(`challenge:${challengeId}:responses`, 0, -1, { rev: true });
    
    if (responseIds.length === 0) {
      return [];
    }

    // Get all responses
    const responses: ChallengeResponse[] = [];
    for (const responseId of responseIds) {
      const responseData = await redis.get(`response:${responseId}`);
      if (responseData) {
        // Upstash Redis client returns objects directly, no need to parse
        const response = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
        responses.push(response as ChallengeResponse);
      }
    }

    return responses;
  } catch (error) {
    console.error('Error getting challenge responses:', error);
    return [];
  }
}

/**
 * Update player statistics
 */
async function updatePlayerStats(playerName: string, challengeId: number, score: number): Promise<void> {
  if (!redis) return;

  try {
    const statsKey = `stats:${playerName}`;
    const existingStats = await redis.get(statsKey);
    
    let stats: PlayerStats;
    if (existingStats) {
      stats = typeof existingStats === 'string' ? JSON.parse(existingStats) : existingStats as PlayerStats;
    } else {
      stats = {
        playerName,
        totalScore: 0,
        challengesCompleted: 0,
        averageScore: 0,
        bestChallenge: { challengeId: 0, score: 0 },
        responses: []
      };
    }

    // Update stats
    stats.totalScore += score;
    stats.challengesCompleted += 1;
    stats.averageScore = Math.round(stats.totalScore / stats.challengesCompleted);

    // Update best challenge if this score is higher
    if (score > stats.bestChallenge.score) {
      stats.bestChallenge = { challengeId, score };
    }

    // Save updated stats
    await redis.set(statsKey, JSON.stringify(stats), { ex: 86400 * 30 }); // 30 days
  } catch (error) {
    console.error('Error updating player stats:', error);
  }
}

/**
 * Get player statistics
 */
export async function getPlayerStats(playerName: string): Promise<PlayerStats | null> {
  if (!redis) {
    console.warn('Redis not available, cannot get player stats');
    return null;
  }

  try {
    const statsKey = `stats:${playerName}`;
    const existingStats = await redis.get(statsKey);
    
    if (existingStats) {
      return typeof existingStats === 'string' ? JSON.parse(existingStats) : existingStats as PlayerStats;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting player stats:', error);
    return null;
  }
}

/**
 * Get top players by total score
 */
export async function getTopPlayers(limit: number = 10): Promise<PlayerStats[]> {
  if (!redis) {
    console.warn('Redis not available, cannot get top players');
    return [];
  }

  try {
    // Get all player names from the leaderboard
    const leaderboardData = await redis.zrange('leaderboard', 0, limit - 1, { withScores: true, rev: true });
    
    const topPlayers: PlayerStats[] = [];
    for (let i = 0; i < leaderboardData.length; i += 2) {
      const playerName = leaderboardData[i] as string;
      const stats = await getPlayerStats(playerName);
      if (stats) {
        topPlayers.push(stats);
      }
    }

    return topPlayers;
  } catch (error) {
    console.error('Error getting top players:', error);
    return [];
  }
}
