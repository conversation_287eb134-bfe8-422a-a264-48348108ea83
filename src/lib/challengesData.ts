
export type ChallengeType = 
  | 'prompt-evaluation' 
  | 'prompt' 
  | 'human-ai' 
  | 'errors' 
  | 'prompt-choice';

export interface BaseChallenge {
  id: number;
  title: string;
  type: ChallengeType;
  timeLimit: number; // en minutos
  description: string;
  instructions?: string;
  points?: number; 
}

export interface PromptEvaluationChallenge extends BaseChallenge {
  type: 'prompt-evaluation';
  genAiFlow: 'evaluatePromptTechnical'; 
  evaluationCriteria: string[];
}

export interface PromptChallenge extends BaseChallenge {
  type: 'prompt';
  genAiFlow: 'scoreRickMortyPrompt'; 
  apiDocLink?: string;
  requiredInfo: string[];
  evaluationCriteria: string[];
}

export interface HumanOrAiSnippet {
  id: string;
  code: string;
  isHuman: boolean;
  explanation: string;
}
export interface HumanOrAiChallenge extends BaseChallenge {
  type: 'human-ai';
  snippets: HumanOrAiSnippet[];
  pointsPerCorrect: number;
}

export interface ErrorToFind {
  id: string;
  description: string; 
  line: number; 
  explanation: string; 
}
export interface HallucinationHunterChallenge extends BaseChallenge {
  type: 'errors';
  codeWithErrors: string;
  errorsToFind: ErrorToFind[];
  pointsPerError: number;
}

export interface PromptChoiceOption {
  id: string; 
  text: string;
}
export interface PromptChoiceScenario {
  id: string;
  taskDescription: string;
  options: PromptChoiceOption[];
  correctOptionId: string;
  explanation: string;
}
export interface BestPromptChallenge extends BaseChallenge {
  type: 'prompt-choice';
  scenarios: PromptChoiceScenario[];
  pointsPerCorrect: number;
}

export type Challenge = 
  | PromptEvaluationChallenge 
  | PromptChallenge 
  | HumanOrAiChallenge 
  | HallucinationHunterChallenge 
  | BestPromptChallenge;

export const challenges: Challenge[] = [
  {
    id: 1,
    title: 'Evaluación de Prompt Técnico (C#)',
    type: 'prompt-evaluation',
    timeLimit: 5,
    description: 'Crea un prompt para generar una aplicación de consola en C# que consulte el Dólar Blue argentino desde una API pública como bluelytics.com.ar. La aplicación debe devolver el valor de compra, valor de venta y fecha de actualización en un solo formato JSON. El desafío es hacer tu prompt lo suficientemente preciso para que la IA infiera una estructura JSON lógica sin que tú la definas explícitamente.',
    instructions: 'Escribe tu prompt en el área de texto. Una IA experta en ingeniería de prompts evaluará rigurosamente su calidad basándose en la claridad, especificidad técnica, completitud y robustez. Recibirás una puntuación y un feedback detallado en ESPAÑOL.',
    genAiFlow: 'evaluatePromptTechnical',
    evaluationCriteria: [ 
      'Especificidad Técnica (Lenguaje C#, tipo de app, API correcta, sugerencias de bibliotecas).',
      'Claridad en Requisitos Funcionales (Extracción de datos específicos, formato JSON mandatorio, guía para estructura JSON implícita).',
      'Robustez y Calidad General (Reducción de ambigüedad, concisión, efectividad).'
    ],
    points: 100,
  },
  {
    id: 2,
    title: 'Info de Personajes de Rick y Morty',
    type: 'prompt',
    timeLimit: 4, 
    description: 'Crea el mejor prompt para obtener información sobre los 5 personajes principales de la API de Rick y Morty. Tu prompt debe apuntar a recuperar nombres, especies, origen, una breve descripción, estado (vivo, muerto, desconocido) y los episodios más frecuentes para estos personajes.',
    instructions: 'Escribe un prompt claro y directo. Una IA experta en ingeniería de prompts analizará tu prompt y te dará una puntuación junto con un feedback detallado en ESPAÑOL sobre su efectividad, claridad y cómo podría mejorarse.',
    apiDocLink: 'https://rickandmortyapi.com/api/character',
    requiredInfo: [ 
      'Nombres de los 5 personajes principales',
      'Sus especies y origen (planeta/dimensión)',
      'Una breve descripción de cada uno',
      'Su estado (vivo, muerto, desconocido)',
      'Episodios en los que aparecen con más frecuencia (nombres o IDs)'
    ],
    genAiFlow: 'scoreRickMortyPrompt',
    evaluationCriteria: [ 
      'Claridad y Especificidad (¿Se piden los 5 personajes principales y todos los datos requeridos?).',
      'Guía y Efectividad (¿Cómo guía el prompt a la IA para identificar personajes y datos?).',
      'Concisión y Formato (¿Es el prompt directo y fácil de parsear?).',
      'Manejo de Ambigüedad (¿Considera casos donde la info podría faltar?).'
    ],
    points: 100,
  },
  {
    id: 3,
    title: '¿Humano o IA?',
    type: 'human-ai',
    timeLimit: 3,
    description: 'Identifica si los siguientes fragmentos de código fueron creados por un humano o una IA. Obtienes 20 puntos por cada identificación correcta.',
    instructions: 'Analiza cada fragmento de código y decide si fue escrito por un programador humano o generado por una Inteligencia Artificial. Considera el estilo, comentarios, complejidad y posibles patrones.',
    snippets: [
      { id: 's1', code: 'function quickSort(arr) {\n  if (arr.length <= 1) return arr;\n\n  const pivot = arr[Math.floor(arr.length / 2)];\n  const left = arr.filter(x => x < pivot);\n  const right = arr.filter(x => x > pivot);\n\n  return [...quickSort(left), pivot, ...quickSort(right)];\n}', isHuman: true, explanation: 'Humano: Nombres descriptivos, estructura clara, común en ejemplos didácticos humanos.' },
      { id: 's2', code: 'const calculateFactorial = (n) => {\n  if (n === 0 || n === 1) {\n    return 1;\n  }\n  return n * calculateFactorial(n - 1);\n};', isHuman: false, explanation: 'IA: Estilo de libro de texto, muy conciso, formato típico de generación de IA para funciones estándar.' },
      { id: 's3', code: "let data = [];\nfor(let i=0;i<items.length;i++){\n    if(items[i].status=='active'){\n        data.push(items[i]);\n    }\n}\nreturn data;", isHuman: true, explanation: 'Humano: Espaciado/estilo inconsistente, bucle for tradicional en lugar de métodos funcionales más modernos que una IA podría preferir.' },
      { id: 's4', code: '/**\n * This function validates email addresses using regular expressions\n * @param {string} email - The email address to validate\n * @returns {boolean} - True if valid, false otherwise\n */\nfunction validateEmail(email) {\n  const emailRegex = /^[^\s@]+@[^\s@]+\\.[^\s@]+$/;\n  return emailRegex.test(email);\n}', isHuman: false, explanation: 'IA: Documentación perfecta en formato JSDoc, estructura formal, regex común para emails.' },
      { id: 's5', code: "const handleClick = (e) => {\n  e.preventDefault();\n  // TODO: fix this later\n  console.log('clicked');\n  doSomething();\n};", isHuman: true, explanation: 'Humano: Comentarios informales como "TODO", uso de console.log para depuración, nombres de función genéricos como "doSomething".' },
    ],
    pointsPerCorrect: 20,
  },
   {
    id: 4,
    title: 'Cazador de Alucinaciones de API',
    type: 'errors',
    timeLimit: 4,
    description: 'El siguiente fragmento de código fue generado por una IA que intenta usar la API de "Gemini", pero comete errores comunes al mezclarla con otras APIs (como OpenAI o Azure). Tu misión es identificar estos errores.',
    instructions: 'Analiza el código cuidadosamente. Haz clic en el número de línea donde creas que hay una confusión o alucinación de la IA. Cada error real que identifiques correctamente sumará puntos. Una vez que hayas marcado todas las líneas que consideres erróneas, envía tu análisis.',
    codeWithErrors: "async function summarizeArticleWithGemini(articleText) {\n  const client = new GeminiApiClient({\n    apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',\n  });\n\n  const serviceStatus = await client.checkStatus();\n  if (!serviceStatus.isOperational) {\n    throw new Error('Gemini service unavailable.');\n  }\n\n  const detectedLanguage = await client.detectLanguageAzure(articleText.substring(0, 50));\n\n  let targetModel = 'gemini-pro-summarizer';\n\n  if (detectedLanguage.code !== 'en') {\n    targetModel = 'azure-cognitive-translator/english-variant';\n  }\n\n  const summary = await client.generateSummary(articleText, {\n    model: targetModel,\n    engine: 'text-davinci-003',\n  });\n\n  return summary.text;\n}",
    errorsToFind: [
      { id: 'e1', line: 3, description: "Formato de clave API de OpenAI ('sk-...') usado con cliente Gemini.", explanation: 'El cliente Gemini espera una API Key de Google. Usar una clave de OpenAI aquí es una confusión de servicios.' },
      { id: 'e2', line: 11, description: "Método 'detectLanguageAzure' es inexistente para un cliente Gemini.", explanation: 'La IA alucinó un método, posiblemente mezclando una funcionalidad de Azure Cognitive Services con el cliente Gemini.' },
      { id: 'e3', line: 16, description: "Identificador de modelo de Azure ('azure-cognitive-translator/...') usado con Gemini.", explanation: 'Este identificador de modelo no es válido para Gemini; Gemini utiliza sus propios identificadores.' },
      { id: 'e4', line: 20, description: "Parámetro 'engine' y modelo 'text-davinci-003' (OpenAI) usados con Gemini.", explanation: "El parámetro 'engine' y modelos como 'text-davinci-003' son específicos de OpenAI, no de Gemini." },
    ],
    pointsPerError: 25,
  },
  {
    id: 5,
    title: 'Elige el Mejor Prompt',
    type: 'prompt-choice',
    timeLimit: 3,
    description: 'Selecciona el prompt más efectivo para cada escenario de desarrollo común para lograr los mejores resultados. Obtienes 30 puntos por cada elección correcta.',
    instructions: 'Lee cada escenario y las opciones de prompt. Elige el prompt que creas que generará el resultado deseado de la manera más precisa y eficiente.',
    scenarios: [
      {
        id: 'pc1',
        taskDescription: 'Generar una función JavaScript para validar emails con regex',
        options: [
          { id: 'A', text: "Crea una función JavaScript moderna y robusta para validar emails usando regex avanzado, que maneje todos los casos edge posibles y siga las mejores prácticas de ES6+." },
          { id: 'B', text: "Crea una función JavaScript llamada 'validateEmail' que tome un string como parámetro y retorne true/false. Debe validar formato de email usando regex, manejar casos edge como emails vacíos, y incluir comentarios explicativos. Proporciona también 3 ejemplos de uso." },
          { id: 'C', text: "Implementa una función de validación de emails en JavaScript que use regex RFC 5322 compliant, con manejo de errores, logging detallado y optimización de performance." },
          { id: 'D', text: "Genera una función validateEmail() en JavaScript con regex pattern robusto, que incluya validación de dominios internacionales, caracteres especiales y casos límite." },
        ],
        correctOptionId: 'B',
        explanation: 'Especifica nombre de función, parámetros, tipo de retorno, técnica y solicita ejemplos, lo que lo hace más completo y dirigido.',
      },
      {
        id: 'pc2',
        taskDescription: 'Debuggear un error de memoria en Python que causa memory leak',
        options: [
          { id: 'A', text: "Como experto en performance de Python, analiza este código con memory leak: [código]. Usa herramientas como memory_profiler, tracemalloc y pympler para identificar el problema y proporciona soluciones optimizadas." },
          { id: 'B', text: "Actúa como un desarrollador Python senior. Analiza este código que tiene un memory leak: [código]. Identifica las causas probables, explica por qué ocurre el problema, proporciona 3 soluciones específicas con código, y sugiere herramientas para profiling de memoria." },
          { id: 'C', text: "Necesito que actúes como un arquitecto Python senior. Revisa este código con problemas de memoria: [código]. Explica las causas del memory leak, implementa weak references donde sea necesario, y optimiza el garbage collection." },
          { id: 'D', text: "Analiza este código Python que consume memoria excesiva: [código]. Identifica patrones anti-pattern, sugiere refactoring con context managers, y proporciona métricas de mejora usando cProfile." },
        ],
        correctOptionId: 'B',
        explanation: 'Define un rol experto, incluye el código problemático, pide un análisis estructurado (causas, explicación, soluciones) y herramientas, siendo muy completo.',
      },
      {
        id: 'pc3',
        taskDescription: 'Crear tests unitarios completos para una clase TypeScript',
        options: [
          { id: 'A', text: "Crea una suite de tests completa en TypeScript usando Jest y Testing Library. Incluye unit tests, integration tests, snapshot testing, y coverage reports con threshold del 95%. Usa mocks avanzados y test doubles." },
          { id: 'B', text: "Genera tests unitarios completos para esta clase TypeScript usando Jest. Incluye: setup/teardown, tests para todos los métodos públicos, casos edge, mocks para dependencias, assertions específicas, y cobertura de al menos 90%. Organiza en blocks describe/it." },
          { id: 'C', text: "Implementa testing comprehensivo para esta clase TypeScript con Jest, Sinon para mocks, y Chai para assertions. Incluye property-based testing, mutation testing, y performance benchmarks." },
          { id: 'D', text: "Desarrolla tests unitarios robustos en TypeScript con Jest framework, incluyendo hooks beforeEach/afterEach, spy functions, mock implementations, y análisis detallado de cobertura de test." },
        ],
        correctOptionId: 'B',
        explanation: 'Especifica el framework de testing (Jest), la estructura de los tests (describe/it), el objetivo de cobertura y qué testear (métodos públicos, casos edge, mocks).',
      },
      {
        id: 'pc4',
        taskDescription: 'Optimizar una consulta SQL que tarda más de 30 segundos',
        options: [
          { id: 'A', text: "Actúa como un Ingeniero de Performance de Base de Datos. Analiza esta query SQL lenta: [query]. Usa EXPLAIN ANALYZE, identifica table scans costosos, sugiere índices compuestos específicos, y reescribe usando CTEs y window functions para optimización." },
          { id: 'B', text: "Como DBA senior, analiza esta consulta SQL que tarda 30+ segundos: [query]. Identifica cuellos de botella, sugiere índices específicos, reescribe la query optimizada, explica el plan de ejecución, y proporciona métricas de mejora esperadas." },
          { id: 'C', text: "Optimiza esta consulta SQL lenta: [query]. Implementa estrategias de particionamiento, hints de query, y vistas materializadas. Proporciona análisis de plan de ejecución y benchmarks de performance antes/después." },
          { id: 'D', text: "Como Especialista en Performance SQL, revisa esta query problemática: [query]. Aplica técnicas de reescritura de query, ajuste de índices y optimización de estadísticas. Incluye queries de monitoreo y alertas de performance." },
        ],
        correctOptionId: 'B',
        explanation: 'Establece contexto de performance, rol experto, análisis técnico específico (cuellos de botella, índices, reescritura, plan de ejecución) y métricas cuantificables.',
      },
      {
        id: 'pc5',
        taskDescription: 'Documentar una API REST en Node.js con todos los endpoints',
        options: [
          { id: 'A', text: "Genera documentación técnica profesional para esta API REST en Node.js: [código]. Crea especificación OpenAPI 3.0, incluye schemas de validación, flujos de autenticación, rate limiting, y ejemplos interactivos con Swagger UI." },
          { id: 'B', text: "Crea documentación completa para esta API REST en Node.js: [código]. Incluye: descripción de cada endpoint, métodos HTTP, parámetros requeridos/opcionales, ejemplos de request/response en JSON, códigos de estado HTTP, y casos de error. Usa formato Markdown." },
          { id: 'C', text: "Documenta esta API REST con estándares enterprise: [código]. Incluye versionado de API, avisos de deprecación, ejemplos de SDK en múltiples lenguajes, métricas de performance, y guías de integración para diferentes frameworks." },
          { id: 'D', text: "Crea documentación API comprehensiva para Node.js: [código]. Implementa docs auto-generados con JSDoc, incluye colecciones Postman, ejemplos curl, guías de manejo de errores, y mejores prácticas para consumidores." },
        ],
        correctOptionId: 'B',
        explanation: 'Define una estructura específica (descripción por endpoint, métodos, params, ejemplos JSON, códigos de estado, errores) y formato de salida (Markdown), cubriendo todos los elementos técnicos necesarios.',
      }
    ],
    pointsPerCorrect: 30,
  },
];
