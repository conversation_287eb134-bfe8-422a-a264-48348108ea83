
import { Redis } from '@upstash/redis';

// Initialize redisClient to null. It will be replaced by a valid instance if successful.
let redisClient: Redis | null = null;

// Try server-side environment variables first, then fall back to client-side
const upstashUrl = process.env.UPSTASH_REDIS_REST_URL ?? process.env.NEXT_PUBLIC_UPSTASH_REDIS_REST_URL;
const upstashToken = process.env.UPSTASH_REDIS_REST_TOKEN ?? process.env.NEXT_PUBLIC_UPSTASH_REDIS_REST_TOKEN;

if (upstashUrl && upstashToken) {
  try {
    console.log('Attempting to initialize Upstash Redis client with URL:', upstashUrl);
    // Create a temporary instance for validation
    const tempRedisInstance = new Redis({
      url: upstashUrl,
      token: upstashToken,
    });
    console.log('Upstash Redis client object nominally created.');

    // Perform strict validation on the temporary instance
    if (tempRedisInstance && typeof tempRedisInstance.zrange === 'function') {
      // If valid, assign it to our module-scoped variable
      redisClient = tempRedisInstance;
      console.log('Upstash Redis client initialized successfully and seems valid. Core method redisClient.zrange is a function.');
    } else {
      // This block executes if the client is malformed
      const instanceDetails = tempRedisInstance
        ? `Instance type: ${Object.prototype.toString.call(tempRedisInstance)}, typeof zrange: ${typeof (tempRedisInstance as any).zrange}, hasOwnProperty('zrange'): ${Object.hasOwn(tempRedisInstance, 'zrange')}, keys: ${JSON.stringify(Object.keys(tempRedisInstance))}`
        : 'tempRedisInstance is null or undefined post-instantiation.';

      console.error(
          `CRITICAL: Failed to initialize Upstash Redis client correctly. Method 'zrange' is missing or not a function on the instance. ${instanceDetails}`
      );
      // redisClient remains null (its initial value), indicating failure.
      console.warn('Upstash Redis client is not valid or malformed. Leaderboard functionality will be disabled. redisClient is null.');
    }
  } catch (e: any) {
    console.error('Exception during Upstash Redis client instantiation or validation:', e.message, e.stack);
    // redisClient remains null, indicating failure.
    console.warn('Leaderboard functionality disabled due to instantiation exception. redisClient is null.');
  }
} else {
  console.warn("Upstash Redis configuration not found or incomplete in environment variables (NEXT_PUBLIC_UPSTASH_REDIS_REST_URL or NEXT_PUBLIC_UPSTASH_REDIS_REST_TOKEN). Leaderboard functionality will be disabled. redisClient is null.");
  // redisClient remains null, indicating failure.
}

// Export the potentially null client, renamed as `redis` for compatibility with existing imports.
export { redisClient as redis };
